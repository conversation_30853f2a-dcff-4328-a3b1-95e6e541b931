import 'package:flutter/material.dart';

class AppColors {
  // Primary Golden Colors
  static const Color primaryGold = Color(0xFFD4AF37);
  static const Color lightGold = Color(0xFFE6C547);
  static const Color darkGold = Color(0xFFB8941F);
  static const Color paleGold = Color(0xFFF5E6A3);
  
  // Background Colors
  static const Color darkBackground = Color(0xFF0D1117);
  static const Color cardBackground = Color(0xFF161B22);
  static const Color surfaceBackground = Color(0xFF21262D);
  static const Color borderColor = Color(0xFF30363D);
  
  // Text Colors
  static const Color primaryText = Color(0xFFF0F6FC);
  static const Color secondaryText = Color(0xFF8B949E);
  static const Color mutedText = Color(0xFF656D76);
  
  // Status Colors
  static const Color success = Color(0xFF238636);
  static const Color warning = Color(0xFFD29922);
  static const Color error = Color(0xFFDA3633);
  static const Color info = Color(0xFF1F6FEB);
  
  // Chart Colors
  static const List<Color> chartColors = [
    primaryGold,
    Color(0xFF58A6FF),
    Color(0xFF7C3AED),
    Color(0xFFEC4899),
    Color(0xFF10B981),
    Color(0xFFF59E0B),
    Color(0xFFEF4444),
    Color(0xFF8B5CF6),
  ];
  
  // Gradient Colors
  static const LinearGradient goldGradient = LinearGradient(
    colors: [lightGold, primaryGold, darkGold],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [darkBackground, surfaceBackground],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
