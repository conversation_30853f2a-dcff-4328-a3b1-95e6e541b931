import 'package:flutter/material.dart';
import '../services/supabase_service.dart';
// import '../core/theme/app_theme.dart'; // unused import
import '../core/constants/app_colors.dart';

class DatabaseExplorerScreen extends StatefulWidget {
  const DatabaseExplorerScreen({super.key});

  @override
  State<DatabaseExplorerScreen> createState() => _DatabaseExplorerScreenState();
}

class _DatabaseExplorerScreenState extends State<DatabaseExplorerScreen> {
  bool _isLoading = false;
  bool _isInitialized = false;
  Map<String, int> _stats = {};
  String _explorationLog = '';

  @override
  void initState() {
    super.initState();
    _initializeSupabase();
  }

  Future<void> _initializeSupabase() async {
    setState(() {
      _isLoading = true;
      _explorationLog = 'جاري تهيئة الاتصال بقاعدة البيانات...\n';
    });

    try {
      await SupabaseService.instance.initialize();
      setState(() {
        _isInitialized = true;
        _explorationLog += '✅ تم الاتصال بقاعدة البيانات بنجاح!\n\n';
      });
      
      await _loadQuickStats();
    } catch (e) {
      setState(() {
        _explorationLog += '❌ فشل في الاتصال: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadQuickStats() async {
    try {
      final stats = await SupabaseService.instance.getQuickStats();
      setState(() {
        _stats = stats;
        _explorationLog += '📊 الإحصائيات السريعة:\n';
        stats.forEach((table, count) {
          _explorationLog += '  • $table: $count سجل\n';
        });
        _explorationLog += '\n';
      });
    } catch (e) {
      setState(() {
        _explorationLog += '❌ خطأ في جلب الإحصائيات: $e\n';
      });
    }
  }

  Future<void> _exploreDatabase() async {
    setState(() {
      _isLoading = true;
      _explorationLog += '🔍 بدء استكشاف تفصيلي لقاعدة البيانات...\n\n';
    });

    try {
      // سنحتاج لتعديل هذا لالتقاط النتائج
      await SupabaseService.instance.exploreDatabase();
      setState(() {
        _explorationLog += '✅ تم الانتهاء من الاستكشاف!\n';
      });
    } catch (e) {
      setState(() {
        _explorationLog += '❌ خطأ في الاستكشاف: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkDevices() async {
    setState(() {
      _isLoading = true;
      _explorationLog += '📱 بدء فحص تفصيلي لجدول الأجهزة...\n\n';
    });

    try {
      await SupabaseService.instance.checkDevicesTable();
      setState(() {
        _explorationLog += '✅ تم الانتهاء من فحص الأجهزة!\n\n';
        _explorationLog += '💡 إذا كان جهازك متصل بتطبيق الكاميرا ولا يظهر هنا،\n';
        _explorationLog += '   فهذا يعني أن تطبيق الكاميرا لا يسجل الأجهزة في قاعدة البيانات.\n\n';
      });
    } catch (e) {
      setState(() {
        _explorationLog += '❌ خطأ في فحص الأجهزة: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // حالة الاتصال
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(
                      _isInitialized ? Icons.check_circle : Icons.error,
                      color: _isInitialized ? AppColors.success : AppColors.error,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _isInitialized ? 'متصل بقاعدة البيانات' : 'غير متصل',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // الإحصائيات السريعة
            if (_stats.isNotEmpty) ...[
              Text(
                'الإحصائيات السريعة',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _stats.entries.map((entry) {
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            entry.value.toString(),
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              color: AppColors.primaryGold,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            entry.key,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],
            
            // أزرار التحكم
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isInitialized && !_isLoading ? _exploreDatabase : null,
                  icon: const Icon(Icons.search),
                  label: const Text('استكشاف تفصيلي'),
                ),
                OutlinedButton.icon(
                  onPressed: _isInitialized && !_isLoading ? _loadQuickStats : null,
                  icon: const Icon(Icons.refresh),
                  label: const Text('تحديث الإحصائيات'),
                ),
                OutlinedButton.icon(
                  onPressed: _isInitialized && !_isLoading ? _checkDevices : null,
                  icon: const Icon(Icons.phone_android),
                  label: const Text('فحص الأجهزة'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // سجل الاستكشاف
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'سجل الاستكشاف',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppColors.surfaceBackground,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppColors.borderColor),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _explorationLog.isEmpty ? 'لا توجد معلومات بعد...' : _explorationLog,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontFamily: 'monospace',
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
  }
}
