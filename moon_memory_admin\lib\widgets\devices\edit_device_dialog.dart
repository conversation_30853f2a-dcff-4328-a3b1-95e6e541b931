import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/devices_provider.dart';
import '../../providers/users_provider.dart';
import '../../models/device_model.dart';

class EditDeviceDialog extends ConsumerStatefulWidget {
  final DeviceModel device;

  const EditDeviceDialog({
    super.key,
    required this.device,
  });

  @override
  ConsumerState<EditDeviceDialog> createState() => _EditDeviceDialogState();
}

class _EditDeviceDialogState extends ConsumerState<EditDeviceDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _deviceNameController;
  late final TextEditingController _modelController;
  late final TextEditingController _descriptionController;

  late String _selectedDeviceType;
  late String? _selectedUserId;
  late String? _selectedLocationId;
  late String _selectedStatus;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _deviceTypes = [
    {'value': 'camera', 'label': 'كاميرا', 'icon': Icons.camera_alt},
    {'value': 'sensor', 'label': 'مستشعر', 'icon': Icons.sensors},
    {'value': 'recorder', 'label': 'مسجل', 'icon': Icons.mic},
    {'value': 'tracker', 'label': 'متتبع', 'icon': Icons.gps_fixed},
  ];

  final List<Map<String, dynamic>> _statusOptions = [
    {'value': 'active', 'label': 'نشط', 'color': AppColors.success},
    {'value': 'inactive', 'label': 'متوقف', 'color': AppColors.warning},
    {'value': 'maintenance', 'label': 'صيانة', 'color': AppColors.info},
  ];

  @override
  void initState() {
    super.initState();
    _deviceNameController = TextEditingController(text: widget.device.deviceName);
    _modelController = TextEditingController(text: widget.device.model);
    _descriptionController = TextEditingController(text: widget.device.description ?? '');
    _selectedDeviceType = widget.device.deviceType;
    _selectedUserId = widget.device.userId;
    _selectedLocationId = widget.device.locationId;
    _selectedStatus = widget.device.status;
  }

  @override
  void dispose() {
    _deviceNameController.dispose();
    _modelController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final usersState = ref.watch(usersProvider);

    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس النافذة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.edit,
                    color: AppColors.info,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تعديل الجهاز',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryText,
                          ),
                        ),
                        Text(
                          'معرف: ${widget.device.deviceId}',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.secondaryText,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: AppColors.secondaryText,
                  ),
                ],
              ),
            ),

            // محتوى النافذة
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معرف الجهاز (للقراءة فقط)
                      TextFormField(
                        initialValue: widget.device.deviceId,
                        decoration: InputDecoration(
                          labelText: 'معرف الجهاز',
                          prefixIcon: const Icon(Icons.tag),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: AppColors.secondaryText.withValues(alpha: 0.1),
                        ),
                        enabled: false,
                      ),

                      const SizedBox(height: 16),

                      // اسم الجهاز
                      TextFormField(
                        controller: _deviceNameController,
                        decoration: InputDecoration(
                          labelText: 'اسم الجهاز *',
                          hintText: 'أدخل اسم الجهاز',
                          prefixIcon: const Icon(Icons.devices),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'اسم الجهاز مطلوب';
                          }
                          if (value!.length < 2) {
                            return 'اسم الجهاز يجب أن يكون حرفين على الأقل';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // نوع الجهاز
                      DropdownButtonFormField<String>(
                        value: _selectedDeviceType,
                        decoration: InputDecoration(
                          labelText: 'نوع الجهاز *',
                          prefixIcon: Icon(_getSelectedTypeIcon()),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: _deviceTypes.map((type) {
                          return DropdownMenuItem<String>(
                            value: type['value'],
                            child: Row(
                              children: [
                                Icon(type['icon'], size: 20),
                                const SizedBox(width: 8),
                                Text(type['label']),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedDeviceType = value!;
                          });
                        },
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'نوع الجهاز مطلوب';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // موديل الجهاز
                      TextFormField(
                        controller: _modelController,
                        decoration: InputDecoration(
                          labelText: 'موديل الجهاز *',
                          hintText: 'أدخل موديل الجهاز',
                          prefixIcon: const Icon(Icons.info),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'موديل الجهاز مطلوب';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // الحالة
                      DropdownButtonFormField<String>(
                        value: _selectedStatus,
                        decoration: InputDecoration(
                          labelText: 'حالة الجهاز *',
                          prefixIcon: Icon(
                            _getStatusIcon(_selectedStatus),
                            color: _getStatusColor(_selectedStatus),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: _statusOptions.map((status) {
                          return DropdownMenuItem<String>(
                            value: status['value'],
                            child: Row(
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: status['color'],
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(status['label']),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        },
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'حالة الجهاز مطلوبة';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // المستخدم المسؤول
                      DropdownButtonFormField<String>(
                        value: _selectedUserId,
                        decoration: InputDecoration(
                          labelText: 'المستخدم المسؤول *',
                          prefixIcon: const Icon(Icons.person),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('اختر المستخدم المسؤول'),
                          ),
                          ...usersState.users.map((user) {
                            return DropdownMenuItem<String>(
                              value: user.id,
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    radius: 12,
                                    backgroundColor: user.isAdmin 
                                        ? AppColors.primaryGold 
                                        : AppColors.info,
                                    child: Text(
                                      user.fullName?.substring(0, 1) ?? 'م',
                                      style: const TextStyle(
                                        color: AppColors.darkBackground,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          user.fullName ?? 'غير محدد',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                        if (user.department?.isNotEmpty == true)
                                          Text(
                                            user.department!,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: AppColors.secondaryText,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedUserId = value;
                          });
                        },
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'المستخدم المسؤول مطلوب';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // الموقع (اختياري)
                      DropdownButtonFormField<String>(
                        value: _selectedLocationId,
                        decoration: InputDecoration(
                          labelText: 'الموقع (اختياري)',
                          prefixIcon: const Icon(Icons.location_on),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem<String>(
                            value: null,
                            child: Text('اختر الموقع (اختياري)'),
                          ),
                          // سيتم تحميل المواقع لاحقاً
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedLocationId = value;
                          });
                        },
                      ),

                      const SizedBox(height: 16),

                      // الوصف
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 3,
                        decoration: InputDecoration(
                          labelText: 'الوصف (اختياري)',
                          hintText: 'أدخل وصف للجهاز',
                          prefixIcon: const Icon(Icons.description),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // أسفل النافذة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.surfaceBackground,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _updateDevice,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.info,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text('حفظ التغييرات'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getSelectedTypeIcon() {
    final type = _deviceTypes.firstWhere((t) => t['value'] == _selectedDeviceType);
    return type['icon'];
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Icons.check_circle;
      case 'inactive':
        return Icons.cancel;
      case 'maintenance':
        return Icons.build;
      default:
        return Icons.help;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return AppColors.success;
      case 'inactive':
        return AppColors.warning;
      case 'maintenance':
        return AppColors.info;
      default:
        return AppColors.secondaryText;
    }
  }

  Future<void> _updateDevice() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await ref.read(devicesProvider.notifier).updateDevice(
        deviceId: widget.device.id,
        deviceName: _deviceNameController.text.trim(),
        deviceType: _selectedDeviceType,
        model: _modelController.text.trim(),
        userId: _selectedUserId!,
        locationId: _selectedLocationId,
        status: _selectedStatus,
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
      );

      if (result['success']) {
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث الجهاز بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'فشل في تحديث الجهاز'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${error.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
