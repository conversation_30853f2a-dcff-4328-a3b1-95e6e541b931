import 'user_model.dart';
import 'location_model.dart';

class DeviceModel {
  final String id;
  final String deviceId;
  final String deviceName;
  final String deviceType;
  final String model;
  final String userId;
  final String? locationId;
  final String? description;
  final String status; // active, inactive, maintenance
  final Map<String, dynamic>? specifications;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final UserModel? user;
  final LocationModel? location;

  const DeviceModel({
    required this.id,
    required this.deviceId,
    required this.deviceName,
    required this.deviceType,
    required this.model,
    required this.userId,
    this.locationId,
    this.description,
    this.status = 'active',
    this.specifications,
    this.createdAt,
    this.updatedAt,
    this.user,
    this.location,
  });

  factory DeviceModel.fromJson(Map<String, dynamic> json) {
    return DeviceModel(
      id: json['id']?.toString() ?? '',
      deviceId: json['device_id']?.toString() ?? '',
      deviceName: json['device_name']?.toString() ?? 'جهاز غير محدد',
      deviceType: json['device_type']?.toString() ?? 'unknown',
      model: json['model']?.toString() ?? 'غير محدد',
      userId: json['user_id']?.toString() ?? '',
      locationId: json['location_id']?.toString(),
      description: json['description']?.toString(),
      status: json['status']?.toString() ?? 'active',
      specifications: json['specifications'] as Map<String, dynamic>?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      user: json['users'] != null
          ? UserModel.fromJson(json['users'] as Map<String, dynamic>)
          : null,
      location: json['locations'] != null
          ? LocationModel.fromJson(json['locations'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'device_id': deviceId,
      'device_name': deviceName,
      'device_type': deviceType,
      'model': model,
      'user_id': userId,
      'location_id': locationId,
      'description': description,
      'status': status,
      'specifications': specifications,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  DeviceModel copyWith({
    String? id,
    String? deviceId,
    String? deviceName,
    String? deviceType,
    String? model,
    String? userId,
    String? locationId,
    String? description,
    String? status,
    Map<String, dynamic>? specifications,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserModel? user,
    LocationModel? location,
  }) {
    return DeviceModel(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      model: model ?? this.model,
      userId: userId ?? this.userId,
      locationId: locationId ?? this.locationId,
      description: description ?? this.description,
      status: status ?? this.status,
      specifications: specifications ?? this.specifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      user: user ?? this.user,
      location: location ?? this.location,
    );
  }

  @override
  String toString() {
    return 'DeviceModel(id: $id, deviceId: $deviceId, deviceName: $deviceName, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
