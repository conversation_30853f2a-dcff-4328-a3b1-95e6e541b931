// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Devices_Sensors_1_H
#define WINRT_Windows_Devices_Sensors_1_H
#include "winrt/impl/Windows.Devices.Sensors.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Sensors
{
    struct __declspec(empty_bases) IAccelerometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer>
    {
        IAccelerometer(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer2>
    {
        IAccelerometer2(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer3>
    {
        IAccelerometer3(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer4>
    {
        IAccelerometer4(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometer5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer5>
    {
        IAccelerometer5(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerDataThreshold>
    {
        IAccelerometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerDeviceId>
    {
        IAccelerometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerReading>
    {
        IAccelerometerReading(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerReading2>
    {
        IAccelerometerReading2(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerReadingChangedEventArgs>
    {
        IAccelerometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerShakenEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerShakenEventArgs>
    {
        IAccelerometerShakenEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerShakenEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerStatics>
    {
        IAccelerometerStatics(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerStatics2>
    {
        IAccelerometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAccelerometerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerStatics3>
    {
        IAccelerometerStatics3(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IActivitySensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensor>
    {
        IActivitySensor(std::nullptr_t = nullptr) noexcept {}
        IActivitySensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IActivitySensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorReading>
    {
        IActivitySensorReading(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IActivitySensorReadingChangeReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorReadingChangeReport>
    {
        IActivitySensorReadingChangeReport(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorReadingChangeReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IActivitySensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorReadingChangedEventArgs>
    {
        IActivitySensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IActivitySensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorStatics>
    {
        IActivitySensorStatics(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IActivitySensorTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorTriggerDetails>
    {
        IActivitySensorTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAltimeter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeter>
    {
        IAltimeter(std::nullptr_t = nullptr) noexcept {}
        IAltimeter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAltimeter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeter2>
    {
        IAltimeter2(std::nullptr_t = nullptr) noexcept {}
        IAltimeter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAltimeterReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterReading>
    {
        IAltimeterReading(std::nullptr_t = nullptr) noexcept {}
        IAltimeterReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAltimeterReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterReading2>
    {
        IAltimeterReading2(std::nullptr_t = nullptr) noexcept {}
        IAltimeterReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAltimeterReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterReadingChangedEventArgs>
    {
        IAltimeterReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAltimeterReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAltimeterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterStatics>
    {
        IAltimeterStatics(std::nullptr_t = nullptr) noexcept {}
        IAltimeterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometer>
    {
        IBarometer(std::nullptr_t = nullptr) noexcept {}
        IBarometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometer2>
    {
        IBarometer2(std::nullptr_t = nullptr) noexcept {}
        IBarometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometer3>
    {
        IBarometer3(std::nullptr_t = nullptr) noexcept {}
        IBarometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerDataThreshold>
    {
        IBarometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IBarometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerReading>
    {
        IBarometerReading(std::nullptr_t = nullptr) noexcept {}
        IBarometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerReading2>
    {
        IBarometerReading2(std::nullptr_t = nullptr) noexcept {}
        IBarometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerReadingChangedEventArgs>
    {
        IBarometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerStatics>
    {
        IBarometerStatics(std::nullptr_t = nullptr) noexcept {}
        IBarometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerStatics2>
    {
        IBarometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IBarometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompass :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass>
    {
        ICompass(std::nullptr_t = nullptr) noexcept {}
        ICompass(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompass2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass2>
    {
        ICompass2(std::nullptr_t = nullptr) noexcept {}
        ICompass2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompass3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass3>
    {
        ICompass3(std::nullptr_t = nullptr) noexcept {}
        ICompass3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompass4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass4>
    {
        ICompass4(std::nullptr_t = nullptr) noexcept {}
        ICompass4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassDataThreshold>
    {
        ICompassDataThreshold(std::nullptr_t = nullptr) noexcept {}
        ICompassDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassDeviceId>
    {
        ICompassDeviceId(std::nullptr_t = nullptr) noexcept {}
        ICompassDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReading>
    {
        ICompassReading(std::nullptr_t = nullptr) noexcept {}
        ICompassReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReading2>
    {
        ICompassReading2(std::nullptr_t = nullptr) noexcept {}
        ICompassReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReadingChangedEventArgs>
    {
        ICompassReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICompassReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassReadingHeadingAccuracy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReadingHeadingAccuracy>
    {
        ICompassReadingHeadingAccuracy(std::nullptr_t = nullptr) noexcept {}
        ICompassReadingHeadingAccuracy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassStatics>
    {
        ICompassStatics(std::nullptr_t = nullptr) noexcept {}
        ICompassStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompassStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassStatics2>
    {
        ICompassStatics2(std::nullptr_t = nullptr) noexcept {}
        ICompassStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer>
    {
        IGyrometer(std::nullptr_t = nullptr) noexcept {}
        IGyrometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer2>
    {
        IGyrometer2(std::nullptr_t = nullptr) noexcept {}
        IGyrometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer3>
    {
        IGyrometer3(std::nullptr_t = nullptr) noexcept {}
        IGyrometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer4>
    {
        IGyrometer4(std::nullptr_t = nullptr) noexcept {}
        IGyrometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerDataThreshold>
    {
        IGyrometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IGyrometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerDeviceId>
    {
        IGyrometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IGyrometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerReading>
    {
        IGyrometerReading(std::nullptr_t = nullptr) noexcept {}
        IGyrometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerReading2>
    {
        IGyrometerReading2(std::nullptr_t = nullptr) noexcept {}
        IGyrometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerReadingChangedEventArgs>
    {
        IGyrometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IGyrometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerStatics>
    {
        IGyrometerStatics(std::nullptr_t = nullptr) noexcept {}
        IGyrometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGyrometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerStatics2>
    {
        IGyrometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IGyrometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHingeAngleReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleReading>
    {
        IHingeAngleReading(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHingeAngleSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleSensor>
    {
        IHingeAngleSensor(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHingeAngleSensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleSensorReadingChangedEventArgs>
    {
        IHingeAngleSensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleSensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHingeAngleSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleSensorStatics>
    {
        IHingeAngleSensorStatics(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer>
    {
        IInclinometer(std::nullptr_t = nullptr) noexcept {}
        IInclinometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer2>
    {
        IInclinometer2(std::nullptr_t = nullptr) noexcept {}
        IInclinometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer3>
    {
        IInclinometer3(std::nullptr_t = nullptr) noexcept {}
        IInclinometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer4>
    {
        IInclinometer4(std::nullptr_t = nullptr) noexcept {}
        IInclinometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerDataThreshold>
    {
        IInclinometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IInclinometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerDeviceId>
    {
        IInclinometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IInclinometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReading>
    {
        IInclinometerReading(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReading2>
    {
        IInclinometerReading2(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReadingChangedEventArgs>
    {
        IInclinometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerReadingYawAccuracy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReadingYawAccuracy>
    {
        IInclinometerReadingYawAccuracy(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReadingYawAccuracy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics>
    {
        IInclinometerStatics(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics2>
    {
        IInclinometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics3>
    {
        IInclinometerStatics3(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInclinometerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics4>
    {
        IInclinometerStatics4(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensor>
    {
        ILightSensor(std::nullptr_t = nullptr) noexcept {}
        ILightSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensor2>
    {
        ILightSensor2(std::nullptr_t = nullptr) noexcept {}
        ILightSensor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensor3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensor3>
    {
        ILightSensor3(std::nullptr_t = nullptr) noexcept {}
        ILightSensor3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensorDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorDataThreshold>
    {
        ILightSensorDataThreshold(std::nullptr_t = nullptr) noexcept {}
        ILightSensorDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensorDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorDeviceId>
    {
        ILightSensorDeviceId(std::nullptr_t = nullptr) noexcept {}
        ILightSensorDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorReading>
    {
        ILightSensorReading(std::nullptr_t = nullptr) noexcept {}
        ILightSensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensorReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorReading2>
    {
        ILightSensorReading2(std::nullptr_t = nullptr) noexcept {}
        ILightSensorReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorReadingChangedEventArgs>
    {
        ILightSensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILightSensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorStatics>
    {
        ILightSensorStatics(std::nullptr_t = nullptr) noexcept {}
        ILightSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILightSensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorStatics2>
    {
        ILightSensorStatics2(std::nullptr_t = nullptr) noexcept {}
        ILightSensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer>
    {
        IMagnetometer(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer2>
    {
        IMagnetometer2(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer3>
    {
        IMagnetometer3(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer4>
    {
        IMagnetometer4(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerDataThreshold>
    {
        IMagnetometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerDeviceId>
    {
        IMagnetometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerReading>
    {
        IMagnetometerReading(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerReading2>
    {
        IMagnetometerReading2(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerReadingChangedEventArgs>
    {
        IMagnetometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerStatics>
    {
        IMagnetometerStatics(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMagnetometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerStatics2>
    {
        IMagnetometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensor>
    {
        IOrientationSensor(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensor2>
    {
        IOrientationSensor2(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensor3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensor3>
    {
        IOrientationSensor3(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensor3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorDeviceId>
    {
        IOrientationSensorDeviceId(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReading>
    {
        IOrientationSensorReading(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReading2>
    {
        IOrientationSensorReading2(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReadingChangedEventArgs>
    {
        IOrientationSensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorReadingYawAccuracy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReadingYawAccuracy>
    {
        IOrientationSensorReadingYawAccuracy(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReadingYawAccuracy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics>
    {
        IOrientationSensorStatics(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics2>
    {
        IOrientationSensorStatics2(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics3>
    {
        IOrientationSensorStatics3(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientationSensorStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics4>
    {
        IOrientationSensorStatics4(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPedometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometer>
    {
        IPedometer(std::nullptr_t = nullptr) noexcept {}
        IPedometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPedometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometer2>
    {
        IPedometer2(std::nullptr_t = nullptr) noexcept {}
        IPedometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPedometerDataThresholdFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerDataThresholdFactory>
    {
        IPedometerDataThresholdFactory(std::nullptr_t = nullptr) noexcept {}
        IPedometerDataThresholdFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPedometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerReading>
    {
        IPedometerReading(std::nullptr_t = nullptr) noexcept {}
        IPedometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPedometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerReadingChangedEventArgs>
    {
        IPedometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPedometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPedometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerStatics>
    {
        IPedometerStatics(std::nullptr_t = nullptr) noexcept {}
        IPedometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPedometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerStatics2>
    {
        IPedometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IPedometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProximitySensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensor>
    {
        IProximitySensor(std::nullptr_t = nullptr) noexcept {}
        IProximitySensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProximitySensorDataThresholdFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorDataThresholdFactory>
    {
        IProximitySensorDataThresholdFactory(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorDataThresholdFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProximitySensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorReading>
    {
        IProximitySensorReading(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProximitySensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorReadingChangedEventArgs>
    {
        IProximitySensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProximitySensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorStatics>
    {
        IProximitySensorStatics(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProximitySensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorStatics2>
    {
        IProximitySensorStatics2(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISensorDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorDataThreshold>
    {
        ISensorDataThreshold(std::nullptr_t = nullptr) noexcept {}
        ISensorDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISensorDataThresholdTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorDataThresholdTriggerDetails>
    {
        ISensorDataThresholdTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        ISensorDataThresholdTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISensorQuaternion :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorQuaternion>
    {
        ISensorQuaternion(std::nullptr_t = nullptr) noexcept {}
        ISensorQuaternion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISensorRotationMatrix :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorRotationMatrix>
    {
        ISensorRotationMatrix(std::nullptr_t = nullptr) noexcept {}
        ISensorRotationMatrix(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISimpleOrientationSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensor>
    {
        ISimpleOrientationSensor(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISimpleOrientationSensor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensor2>
    {
        ISimpleOrientationSensor2(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISimpleOrientationSensorDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorDeviceId>
    {
        ISimpleOrientationSensorDeviceId(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISimpleOrientationSensorOrientationChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorOrientationChangedEventArgs>
    {
        ISimpleOrientationSensorOrientationChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorOrientationChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISimpleOrientationSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorStatics>
    {
        ISimpleOrientationSensorStatics(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISimpleOrientationSensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorStatics2>
    {
        ISimpleOrientationSensorStatics2(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
