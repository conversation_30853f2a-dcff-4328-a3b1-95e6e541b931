import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/supabase_service.dart';
import '../services/admin_supabase_service.dart';
import '../models/user_model.dart';

// Users Filter Model
class UsersFilter {
  final String? searchQuery;
  final bool? isActive;
  final bool? isAdmin;
  final String? department;
  final String? accountType;

  const UsersFilter({
    this.searchQuery,
    this.isActive,
    this.isAdmin,
    this.department,
    this.accountType,
  });

  UsersFilter copyWith({
    String? searchQuery,
    bool? isActive,
    bool? isAdmin,
    String? department,
    String? accountType,
  }) {
    return UsersFilter(
      searchQuery: searchQuery ?? this.searchQuery,
      isActive: isActive ?? this.isActive,
      isAdmin: isAdmin ?? this.isAdmin,
      department: department ?? this.department,
      accountType: accountType ?? this.accountType,
    );
  }

  bool get hasActiveFilters =>
      searchQuery?.isNotEmpty == true ||
      isActive != null ||
      isAdmin != null ||
      department?.isNotEmpty == true ||
      accountType?.isNotEmpty == true;
}

// Users State
class UsersState {
  final List<UserModel> users;
  final bool isLoading;
  final String? error;
  final UsersFilter filter;
  final int totalCount;
  final int currentPage;
  final int pageSize;

  const UsersState({
    required this.users,
    required this.isLoading,
    this.error,
    required this.filter,
    required this.totalCount,
    required this.currentPage,
    required this.pageSize,
  });

  UsersState copyWith({
    List<UserModel>? users,
    bool? isLoading,
    String? error,
    UsersFilter? filter,
    int? totalCount,
    int? currentPage,
    int? pageSize,
  }) {
    return UsersState(
      users: users ?? this.users,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      filter: filter ?? this.filter,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
    );
  }

  int get totalPages => (totalCount / pageSize).ceil();
  bool get hasNextPage => currentPage < totalPages;
  bool get hasPreviousPage => currentPage > 1;
}

// Users Provider
class UsersNotifier extends StateNotifier<UsersState> {
  UsersNotifier() : super(const UsersState(
    users: [],
    isLoading: false,
    filter: UsersFilter(),
    totalCount: 0,
    currentPage: 1,
    pageSize: 20,
  )) {
    loadUsers();
  }

  Future<void> loadUsers({bool reset = false}) async {
    if (reset) {
      state = state.copyWith(currentPage: 1);
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final supabase = SupabaseService.instance.client;
      
      // استعلام بسيط لجلب جميع المستخدمين
      final response = await supabase
          .from('users')
          .select()
          .order('created_at', ascending: false);
      
      // جلب العدد الإجمالي (تبسيط مؤقت)
      final countResponse = await supabase.from('users').select('*').count();
      
      final users = response.map((json) => UserModel.fromJson(json)).toList();
      
      state = state.copyWith(
        users: users,
        isLoading: false,
        totalCount: countResponse.count,
      );
      
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  void updateFilter(UsersFilter newFilter) {
    state = state.copyWith(filter: newFilter, currentPage: 1);
    loadUsers();
  }

  void clearFilter() {
    state = state.copyWith(filter: const UsersFilter(), currentPage: 1);
    loadUsers();
  }

  void nextPage() {
    if (state.hasNextPage) {
      state = state.copyWith(currentPage: state.currentPage + 1);
      loadUsers();
    }
  }

  void previousPage() {
    if (state.hasPreviousPage) {
      state = state.copyWith(currentPage: state.currentPage - 1);
      loadUsers();
    }
  }

  void goToPage(int page) {
    if (page >= 1 && page <= state.totalPages) {
      state = state.copyWith(currentPage: page);
      loadUsers();
    }
  }

  Future<void> toggleUserStatus(String userId, bool isActive) async {
    try {
      // استخدام الخدمة الإدارية
      final result = await AdminSupabaseService.instance.toggleUserStatus(
        userId: userId,
        isActive: isActive,
      );

      if (result['success']) {
        // تحديث القائمة المحلية
        final updatedUsers = state.users.map((user) {
          if (user.id == userId) {
            return user.copyWith(isActive: isActive);
          }
          return user;
        }).toList();

        state = state.copyWith(users: updatedUsers);
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }

    } catch (error) {
      state = state.copyWith(error: 'فشل في تغيير حالة المستخدم: ${error.toString()}');
      rethrow;
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      // استخدام الخدمة الإدارية
      final result = await AdminSupabaseService.instance.deleteUser(
        userId: userId,
      );

      if (result['success']) {
        // إعادة تحميل القائمة
        await loadUsers();
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }

    } catch (error) {
      state = state.copyWith(error: 'فشل في حذف المستخدم: ${error.toString()}');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createUser({
    required String nationalId,
    required String fullName,
    String? phone,
    required String department,
    required String position,
    required String password,
    required bool isAdmin,
    required bool isActive,
  }) async {
    try {
      // استخدام الخدمة الإدارية لإنشاء المستخدم
      final result = await AdminSupabaseService.instance.createUser(
        nationalId: nationalId,
        fullName: fullName,
        phone: phone,
        department: department,
        position: position,
        password: password,
        isAdmin: isAdmin,
        isActive: isActive,
      );

      if (result['success']) {
        // إعادة تحميل القائمة
        await loadUsers();
        // مسح رسالة الخطأ السابقة
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
      }

      return result;

    } catch (error) {
      final errorMessage = 'فشل في إنشاء المستخدم: ${error.toString()}';
      state = state.copyWith(error: errorMessage);
      return {
        'success': false,
        'error': errorMessage,
      };
    }
  }

  /// حذف مستخدم بالرقم الوطني (للتنظيف)
  Future<void> deleteUserByNationalId(String nationalId) async {
    try {
      final result = await AdminSupabaseService.instance.deleteUserByNationalId(
        nationalId: nationalId,
      );

      if (result['success']) {
        await loadUsers();
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }

    } catch (error) {
      state = state.copyWith(error: 'فشل في حذف المستخدم: ${error.toString()}');
      rethrow;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(error: null);
  }

  void refresh() {
    loadUsers(reset: true);
  }
}

// Provider
final usersProvider = StateNotifierProvider<UsersNotifier, UsersState>((ref) {
  return UsersNotifier();
});

// Helper providers
final filteredUsersCountProvider = Provider<int>((ref) {
  final usersState = ref.watch(usersProvider);
  return usersState.totalCount;
});

final activeUsersCountProvider = Provider<int>((ref) {
  final usersState = ref.watch(usersProvider);
  return usersState.users.where((user) => user.isActive).length;
});

final adminUsersCountProvider = Provider<int>((ref) {
  final usersState = ref.watch(usersProvider);
  return usersState.users.where((user) => user.isAdmin).length;
});
