import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_colors.dart';
import '../providers/devices_provider.dart';
import '../widgets/devices/devices_stats_bar.dart';
import '../widgets/devices/devices_filter_bar.dart';
import '../widgets/devices/devices_data_table.dart';
import '../widgets/devices/create_device_dialog.dart';

class DevicesScreen extends ConsumerWidget {
  const DevicesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final devicesState = ref.watch(devicesProvider);

    return Scaffold(
      backgroundColor: AppColors.surfaceBackground,
      body: Column(
        children: [
          // أزرار الإجراءات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            child: Row(
              children: [
                const Spacer(),
                OutlinedButton.icon(
                  onPressed: () {
                    ref.read(devicesProvider.notifier).refresh();
                  },
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('تحديث القائمة', style: TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: Size.zero,
                  ),
                ),
              ],
            ),
          ),

          // شريط الإحصائيات
          const DevicesStatsBar(),

          // شريط الفلترة والبحث
          const DevicesFilterBar(),

          // جدول الأجهزة
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(24),
              child: Card(
                elevation: 1,
                color: AppColors.cardBackground,
                child: Column(
                  children: [
                    // رأس الجدول
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.surfaceBackground,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(
                            'قائمة الأجهزة',
                            style: const TextStyle(
                              color: AppColors.primaryGold,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          if (devicesState.isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: AppColors.primaryGold,
                              ),
                            ),
                          const SizedBox(width: 16),
                          Text(
                            '${devicesState.totalCount} جهاز',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.secondaryText,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // محتوى الجدول
                    Expanded(
                      child: devicesState.error != null
                          ? _buildErrorState(context, devicesState.error!, ref)
                          : devicesState.devices.isEmpty && !devicesState.isLoading
                              ? _buildEmptyState(context)
                              : const DevicesDataTable(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل الأجهزة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(devicesProvider.notifier).refresh();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryGold,
              foregroundColor: AppColors.darkBackground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.devices,
            size: 64,
            color: AppColors.secondaryText,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أجهزة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أي أجهزة مطابقة للفلاتر المحددة',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showCreateDeviceDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('إضافة جهاز جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryGold,
              foregroundColor: AppColors.darkBackground,
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateDeviceDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CreateDeviceDialog(),
    );
  }
}
