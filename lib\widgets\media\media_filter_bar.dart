import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/media_provider.dart';

class MediaFilterBar extends ConsumerStatefulWidget {
  const MediaFilterBar({super.key});

  @override
  ConsumerState<MediaFilterBar> createState() => _MediaFilterBarState();
}

class _MediaFilterBarState extends ConsumerState<MediaFilterBar> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedMediaType = 'all';
  String? _selectedUser;
  String? _selectedDevice;
  String? _selectedLocation;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: Card(
        elevation: 1,
        color: AppColors.cardBackground,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  // حقل البحث
                  Expanded(
                    flex: 3,
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'البحث في الوسائط (اسم الملف، الوصف...)',
                        prefixIcon: const Icon(Icons.search, color: AppColors.primaryGold),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  ref.read(mediaProvider.notifier).searchMedia('');
                                },
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: AppColors.primaryGold.withValues(alpha: 0.3)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: AppColors.primaryGold.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: AppColors.primaryGold),
                        ),
                        filled: true,
                        fillColor: AppColors.surfaceBackground,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      style: const TextStyle(color: AppColors.primaryText),
                      onChanged: (value) {
                        setState(() {});
                        // تأخير البحث لتحسين الأداء
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (_searchController.text == value) {
                            ref.read(mediaProvider.notifier).searchMedia(value);
                          }
                        });
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // فلتر نوع الوسائط
                  SizedBox(
                    width: 140,
                    child: DropdownButtonFormField<String>(
                      value: _selectedMediaType,
                      decoration: InputDecoration(
                        labelText: 'نوع الوسائط',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'all',
                          child: Row(
                            children: [
                              Icon(Icons.perm_media, size: 16),
                              SizedBox(width: 8),
                              Text('الكل'),
                            ],
                          ),
                        ),
                        DropdownMenuItem(
                          value: 'photos',
                          child: Row(
                            children: [
                              Icon(Icons.photo, size: 16),
                              SizedBox(width: 8),
                              Text('صور'),
                            ],
                          ),
                        ),
                        DropdownMenuItem(
                          value: 'videos',
                          child: Row(
                            children: [
                              Icon(Icons.videocam, size: 16),
                              SizedBox(width: 8),
                              Text('فيديوهات'),
                            ],
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedMediaType = value!);
                        ref.read(mediaProvider.notifier).filterByMediaType(value!);
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // فلتر التاريخ
                  SizedBox(
                    width: 160,
                    child: OutlinedButton.icon(
                      onPressed: () => _showDateRangePicker(context),
                      icon: const Icon(Icons.date_range, size: 16),
                      label: Text(
                        _getDateRangeText(),
                        style: const TextStyle(fontSize: 12),
                      ),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        minimumSize: Size.zero,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر مسح الفلاتر
                  OutlinedButton.icon(
                    onPressed: _hasActiveFilters() ? _clearFilters : null,
                    icon: const Icon(Icons.clear_all, size: 18),
                    label: const Text('مسح الفلاتر', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      minimumSize: Size.zero,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر رفع الوسائط
                  ElevatedButton.icon(
                    onPressed: () => _showUploadDialog(context),
                    icon: const Icon(Icons.cloud_upload, size: 18),
                    label: const Text('رفع وسائط', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryGold,
                      foregroundColor: AppColors.darkBackground,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      minimumSize: Size.zero,
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),

              // فلاتر إضافية (صف ثاني)
              if (_hasAdvancedFilters()) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    // فلتر المستخدم
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedUser,
                        decoration: InputDecoration(
                          labelText: 'المستخدم',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('جميع المستخدمين')),
                          // سيتم تحميل المستخدمين ديناميكياً
                        ],
                        onChanged: (value) {
                          setState(() => _selectedUser = value);
                          ref.read(mediaProvider.notifier).filterByUser(value);
                        },
                      ),
                    ),

                    const SizedBox(width: 12),

                    // فلتر الجهاز
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedDevice,
                        decoration: InputDecoration(
                          labelText: 'الجهاز',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('جميع الأجهزة')),
                          // سيتم تحميل الأجهزة ديناميكياً
                        ],
                        onChanged: (value) {
                          setState(() => _selectedDevice = value);
                          ref.read(mediaProvider.notifier).filterByDevice(value);
                        },
                      ),
                    ),

                    const SizedBox(width: 12),

                    // زر إظهار/إخفاء الفلاتر المتقدمة
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          // تبديل حالة الفلاتر المتقدمة
                        });
                      },
                      icon: const Icon(Icons.expand_less),
                      label: const Text('إخفاء الفلاتر المتقدمة'),
                    ),
                  ],
                ),
              ] else ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton.icon(
                    onPressed: () {
                      setState(() {
                        // تبديل حالة الفلاتر المتقدمة
                      });
                    },
                    icon: const Icon(Icons.expand_more),
                    label: const Text('فلاتر متقدمة'),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _getDateRangeText() {
    if (_startDate != null && _endDate != null) {
      return '${_formatDate(_startDate!)} - ${_formatDate(_endDate!)}';
    } else if (_startDate != null) {
      return 'من ${_formatDate(_startDate!)}';
    } else if (_endDate != null) {
      return 'إلى ${_formatDate(_endDate!)}';
    }
    return 'فترة زمنية';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  bool _hasActiveFilters() {
    return _searchController.text.isNotEmpty ||
        _selectedMediaType != 'all' ||
        _selectedUser != null ||
        _selectedDevice != null ||
        _selectedLocation != null ||
        _startDate != null ||
        _endDate != null;
  }

  bool _hasAdvancedFilters() {
    // يمكن تحديد متى تظهر الفلاتر المتقدمة
    return false; // مؤقتاً
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedMediaType = 'all';
      _selectedUser = null;
      _selectedDevice = null;
      _selectedLocation = null;
      _startDate = null;
      _endDate = null;
    });
    
    ref.read(mediaProvider.notifier).clearFilters();
  }

  void _showDateRangePicker(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primaryGold,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      ref.read(mediaProvider.notifier).filterByDateRange(_startDate, _endDate);
    }
  }

  void _showUploadDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رفع وسائط'),
        content: const Text('سيتم تطوير ميزة رفع الوسائط قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
