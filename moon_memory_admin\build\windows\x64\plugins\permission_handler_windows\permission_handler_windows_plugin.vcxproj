﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F9D91351-2C5C-3A24-A38C-B1664CF29155}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>permission_handler_windows_plugin</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">permission_handler_windows_plugin.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">permission_handler_windows_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">permission_handler_windows_plugin.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">permission_handler_windows_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">permission_handler_windows_plugin.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">permission_handler_windows_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";permission_handler_windows_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";permission_handler_windows_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\flutter\Debug\flutter_wrapper_plugin.lib;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/Debug/permission_handler_windows_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/Debug/permission_handler_windows_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Profile";permission_handler_windows_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\";permission_handler_windows_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\flutter\Profile\flutter_wrapper_plugin.lib;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/Profile/permission_handler_windows_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/Profile/permission_handler_windows_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Release";permission_handler_windows_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";permission_handler_windows_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\flutter\Release\flutter_wrapper_plugin.lib;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/Release/permission_handler_windows_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/Release/permission_handler_windows_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\CMakeLists.txt">
      <StdOutEncoding>UTF-8</StdOutEncoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FetchContent.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FetchContent.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/plugins/permission_handler_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FetchContent.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include\permission_handler_windows\permission_handler_windows_plugin.h" />
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\permission_handler_windows_plugin.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{********-62BB-3A53-A41E-0C81E34DD81F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{2B17AEB8-E140-332E-83A0-0B29EAD2D143}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>