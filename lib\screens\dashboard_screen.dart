import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_colors.dart';
import '../providers/dashboard_provider.dart';
import '../widgets/dashboard/stats_card.dart';
import '../widgets/dashboard/chart_card.dart';
import '../widgets/dashboard/recent_activity_card.dart';
import '../widgets/dashboard/top_locations_card.dart';
import '../services/supabase_service.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardState = ref.watch(dashboardProvider);

    return dashboardState.when(
      data: (stats) => _buildDashboard(context, stats),
      loading: () => _buildLoading(),
      error: (error, stack) => _buildError(context, error, ref),
    );
  }

  Widget _buildDashboard(BuildContext context, DashboardStats stats) {
    return RefreshIndicator(
      onRefresh: () async {
        // سيتم تنفيذ التحديث لاحقاً
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // أزرار الإجراءات السريعة
            Row(
              children: [
                const Spacer(),
                Consumer(
                  builder: (context, ref, child) {
                    return Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: () {
                            ref.read(dashboardProvider.notifier).refresh();
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('تحديث البيانات'),
                        ),
                        const SizedBox(width: 12),
                        OutlinedButton.icon(
                          onPressed: () {
                            _showDeviceCheckDialog(context);
                          },
                          icon: const Icon(Icons.phone_android),
                          label: const Text('فحص الأجهزة'),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),
            
            // بطاقات الإحصائيات الرئيسية
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 4,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                StatsCard(
                  title: 'إجمالي المستخدمين',
                  value: stats.totalUsers.toString(),
                  subtitle: '${stats.activeUsers} نشط',
                  icon: Icons.people,
                  color: AppColors.primaryGold,
                ),
                StatsCard(
                  title: 'إجمالي المواقع',
                  value: stats.totalLocations.toString(),
                  subtitle: '${stats.activeLocations} نشط',
                  icon: Icons.location_on,
                  color: AppColors.info,
                ),
                StatsCard(
                  title: 'إجمالي الصور',
                  value: stats.totalPhotos.toString(),
                  subtitle: 'صورة محفوظة',
                  icon: Icons.photo,
                  color: AppColors.success,
                ),
                StatsCard(
                  title: 'إجمالي الفيديوهات',
                  value: stats.totalVideos.toString(),
                  subtitle: 'فيديو محفوظ',
                  icon: Icons.videocam,
                  color: AppColors.warning,
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // الصف الثاني من البطاقات
            Row(
              children: [
                // الرسوم البيانية
                Expanded(
                  flex: 2,
                  child: ChartCard(
                    totalPhotos: stats.totalPhotos,
                    totalVideos: stats.totalVideos,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // أكثر المواقع استخداماً
                Expanded(
                  flex: 1,
                  child: TopLocationsCard(
                    locations: stats.topLocations,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // النشاطات الحديثة
            RecentActivityCard(
              recentUsers: stats.recentUsers,
              recentPhotos: stats.recentPhotos,
              recentVideos: stats.recentVideos,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primaryGold,
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildError(BuildContext context, Object error, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(dashboardProvider.notifier).refresh();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  void _showDeviceCheckDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const DeviceCheckDialog(),
    );
  }
}

class DeviceCheckDialog extends StatefulWidget {
  const DeviceCheckDialog({super.key});

  @override
  State<DeviceCheckDialog> createState() => _DeviceCheckDialogState();
}

class _DeviceCheckDialogState extends State<DeviceCheckDialog> {
  bool _isLoading = false;
  String _result = '';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.phone_android,
            color: AppColors.primaryGold,
          ),
          const SizedBox(width: 12),
          const Text('فحص الأجهزة'),
        ],
      ),
      content: SizedBox(
        width: 500,
        height: 400,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فحص تفصيلي لجدول الأجهزة في قاعدة البيانات',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.secondaryText,
              ),
            ),

            const SizedBox(height: 16),

            ElevatedButton.icon(
              onPressed: _isLoading ? null : _checkDevices,
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.search),
              label: Text(_isLoading ? 'جاري الفحص...' : 'بدء الفحص'),
            ),

            const SizedBox(height: 16),

            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.surfaceBackground,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.borderColor),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result.isEmpty ? 'اضغط "بدء الفحص" لفحص الأجهزة...' : _result,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                      color: AppColors.primaryText,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  Future<void> _checkDevices() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري فحص جدول الأجهزة...\n\n';
    });

    try {
      // جلب جميع الأجهزة
      final supabase = SupabaseService.instance.client;
      final allDevices = await supabase.from('devices').select();

      setState(() {
        _result += '📱 إجمالي الأجهزة في قاعدة البيانات: ${allDevices.length}\n\n';
      });

      if (allDevices.isNotEmpty) {
        setState(() {
          _result += '🔹 الأجهزة الموجودة:\n';
        });

        for (var device in allDevices) {
          setState(() {
            _result += '  • ID: ${device['id']}\n';
            _result += '    User ID: ${device['user_id']}\n';
            _result += '    Device Name: ${device['device_name'] ?? 'غير محدد'}\n';
            _result += '    Device Type: ${device['device_type'] ?? 'غير محدد'}\n';
            _result += '    Is Active: ${device['is_active'] ?? 'غير محدد'}\n';
            _result += '    Last Active: ${device['last_active_at'] ?? 'غير محدد'}\n';
            _result += '    Created At: ${device['created_at'] ?? 'غير محدد'}\n\n';
          });
        }
      } else {
        setState(() {
          _result += '⚠️  لا توجد أجهزة مسجلة في قاعدة البيانات\n\n';
          _result += '💡 هذا يعني أن تطبيق الكاميرا لم يسجل أي جهاز بعد.\n\n';
          _result += '🔧 الحلول المقترحة:\n';
          _result += '  1. تأكد من تسجيل الدخول في تطبيق الكاميرا\n';
          _result += '  2. التقط صورة أو فيديو من تطبيق الكاميرا\n';
          _result += '  3. تحقق من أن تطبيق الكاميرا يحتوي على كود تسجيل الأجهزة\n\n';
        });
      }

      // فحص المستخدمين النشطين
      final activeUsers = await supabase
          .from('users')
          .select()
          .eq('is_active', true);

      setState(() {
        _result += '👥 المستخدمين النشطين: ${activeUsers.length}\n\n';
      });

      // فحص آخر نشاط
      final recentPhotos = await supabase
          .from('photos')
          .select('user_id, username, upload_timestamp')
          .order('upload_timestamp', ascending: false)
          .limit(3);

      if (recentPhotos.isNotEmpty) {
        setState(() {
          _result += '📸 آخر الصور المرفوعة:\n';
        });
        for (var photo in recentPhotos) {
          setState(() {
            _result += '  • ${photo['username']} - ${photo['upload_timestamp']}\n';
          });
        }
      }

      setState(() {
        _result += '\n✅ تم الانتهاء من الفحص!';
      });

    } catch (e) {
      setState(() {
        _result += '❌ خطأ في فحص الأجهزة: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
