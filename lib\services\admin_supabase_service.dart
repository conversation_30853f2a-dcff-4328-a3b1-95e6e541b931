import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/constants/app_constants.dart';
import 'package:flutter/foundation.dart';

/// خدمة Supabase الإدارية مع صلاحيات كاملة
/// تستخدم Service Role Key لإنشاء وإدارة المستخدمين
class AdminSupabaseService {
  static AdminSupabaseService? _instance;
  static AdminSupabaseService get instance => _instance ??= AdminSupabaseService._();
  
  AdminSupabaseService._();
  
  SupabaseClient? _adminClient;
  SupabaseClient get adminClient => _adminClient!;
  
  bool get isInitialized => _adminClient != null;
  
  /// تهيئة العميل الإداري
  Future<void> initialize() async {
    if (_adminClient != null) return;
    
    // استخدام Service Role Key للصلاحيات الكاملة
    _adminClient = SupabaseClient(
      AppConstants.supabaseUrl,
      AppConstants.supabaseServiceRoleKey, // مفتاح الخدمة الإدارية
      authOptions: const AuthClientOptions(
        autoRefreshToken: false,
      ),
    );
    
    debugPrint('✅ Admin Supabase client initialized successfully');
  }
  
  /// إنشاء مستخدم جديد مع إمكانية تسجيل الدخول
  /// يتم إنشاء البريد الإلكتروني تلقائياً: رقم_الهوية@moon-memory.com
  Future<Map<String, dynamic>> createUser({
    required String nationalId,
    required String fullName,
    String? phone,
    required String department,
    required String position,
    required String password,
    required bool isAdmin,
    required bool isActive,
  }) async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      // إنشاء البريد الإلكتروني تلقائياً
      final email = '$<EMAIL>';

      // 0. التحقق من وجود الرقم الوطني مسبقاً
      debugPrint('🔍 التحقق من وجود الرقم الوطني: $nationalId');

      try {
        final existingUser = await _adminClient!
            .from('users')
            .select('id, national_id, full_name')
            .eq('national_id', nationalId)
            .maybeSingle();

        if (existingUser != null) {
          debugPrint('⚠️ يوجد مستخدم بهذا الرقم الوطني: ${existingUser['full_name']}');
          return {
            'success': false,
            'error': 'يوجد مستخدم بالرقم الوطني $nationalId مسبقاً (${existingUser['full_name']}). يرجى حذفه أولاً أو استخدام رقم وطني مختلف.',
          };
        }

        debugPrint('✅ الرقم الوطني متاح للاستخدام');
      } catch (checkError) {
        debugPrint('❌ خطأ في التحقق من الرقم الوطني: $checkError');
        return {
          'success': false,
          'error': 'خطأ في التحقق من الرقم الوطني: ${checkError.toString()}',
        };
      }

      // 1. إنشاء المستخدم في نظام المصادقة أولاً
      late final String userId;

      try {
        final authResponse = await _adminClient!.auth.admin.createUser(
          AdminUserAttributes(
            email: email,
            password: password,
            emailConfirm: true, // تأكيد البريد الإلكتروني تلقائياً
            userMetadata: {
              'full_name': fullName,
              'national_id': nationalId,
              'department': department,
              'position': position,
            },
          ),
        );

        if (authResponse.user == null) {
          return {
            'success': false,
            'error': 'فشل في إنشاء المستخدم في نظام المصادقة',
          };
        }

        userId = authResponse.user!.id;
        debugPrint('✅ تم إنشاء المستخدم في نظام المصادقة: $userId');

      } catch (authError) {
        debugPrint('❌ خطأ في إنشاء المستخدم في نظام المصادقة: $authError');

        // إذا كان الخطأ بسبب وجود البريد الإلكتروني، نحاول حذف المستخدم القديم
        if (authError.toString().contains('already been registered')) {
          debugPrint('🔄 محاولة حذف المستخدم القديم وإعادة الإنشاء...');

          try {
            // البحث عن المستخدم في جدول users بالرقم الوطني
            final existingUserInDB = await _adminClient!
                .from('users')
                .select('id, email, full_name')
                .eq('national_id', nationalId)
                .maybeSingle();

            if (existingUserInDB != null) {
              debugPrint('🔍 تم العثور على المستخدم في قاعدة البيانات: ${existingUserInDB['full_name']}');

              // حذف المستخدم من نظام المصادقة
              try {
                await _adminClient!.auth.admin.deleteUser(existingUserInDB['id']);
                debugPrint('✅ تم حذف المستخدم من نظام المصادقة');
              } catch (authDeleteError) {
                debugPrint('⚠️ لم يتم العثور على المستخدم في نظام المصادقة أو تم حذفه مسبقاً');
              }

              // حذف المستخدم من جدول users
              await _adminClient!
                  .from('users')
                  .delete()
                  .eq('id', existingUserInDB['id']);
              debugPrint('✅ تم حذف المستخدم من قاعدة البيانات');

              // إعادة المحاولة لإنشاء المستخدم
              debugPrint('🔄 إعادة محاولة إنشاء المستخدم...');

              final retryAuthResponse = await _adminClient!.auth.admin.createUser(
                AdminUserAttributes(
                  email: email,
                  password: password,
                  emailConfirm: true,
                  userMetadata: {
                    'full_name': fullName,
                    'national_id': nationalId,
                    'department': department,
                    'position': position,
                  },
                ),
              );

              if (retryAuthResponse.user == null) {
                return {
                  'success': false,
                  'error': 'فشل في إعادة إنشاء المستخدم في نظام المصادقة',
                };
              }

              userId = retryAuthResponse.user!.id;
              debugPrint('✅ تم إعادة إنشاء المستخدم في نظام المصادقة: $userId');

            } else {
              return {
                'success': false,
                'error': 'يوجد مستخدم بهذا البريد الإلكتروني في نظام المصادقة ولكن ليس في قاعدة البيانات. يرجى المحاولة مرة أخرى.',
              };
            }
          } catch (deleteError) {
            debugPrint('❌ فشل في حذف المستخدم القديم: $deleteError');
            return {
              'success': false,
              'error': 'يوجد مستخدم بهذا الرقم الوطني مسبقاً ولا يمكن حذفه تلقائياً: ${deleteError.toString()}',
            };
          }
        }

        return {
          'success': false,
          'error': 'خطأ في إنشاء المستخدم: ${authError.toString()}',
        };
      }

      // 2. إنشاء المستخدم في جدول المستخدمين العام
      await _adminClient!.from('users').insert({
        'id': userId,
        'national_id': nationalId,
        'full_name': fullName,
        'email': email,
        'phone': phone,
        'department': department,
        'position': position,
        'is_active': isActive,
        'is_admin': isAdmin,
        'account_type': isAdmin ? 'admin' : 'user',
        'max_devices': 1,
        'storage_quota_mb': isAdmin ? 10000 : 5000,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      return {
        'success': true,
        'user_id': userId,
        'national_id': nationalId,
        'email': email,
        'password': password,
        'message': 'تم إنشاء المستخدم بنجاح',
      };

    } catch (error) {
      return {
        'success': false,
        'error': 'خطأ في إنشاء المستخدم: ${error.toString()}',
      };
    }
  }

  /// إعادة تعيين كلمة مرور المستخدم
  Future<Map<String, dynamic>> resetPassword({
    required String userId,
    required String newPassword,
  }) async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('🔄 بدء تغيير كلمة المرور للمستخدم: $userId');
      }

      final response = await _adminClient!.auth.admin.updateUserById(
        userId,
        attributes: AdminUserAttributes(
          password: newPassword,
          emailConfirm: true, // تأكيد البريد الإلكتروني
        ),
      );

      if (kDebugMode) {
        print('✅ تم تغيير كلمة المرور بنجاح للمستخدم: $userId');
        print('📧 معرف المستخدم المحدث: ${response.user?.id}');
      }

      return {
        'success': true,
        'message': 'تم إعادة تعيين كلمة المرور بنجاح',
        'user_id': userId,
      };
    } catch (error) {
      if (kDebugMode) {
        print('❌ خطأ في تغيير كلمة المرور: $error');
      }

      return {
        'success': false,
        'error': 'خطأ في إعادة تعيين كلمة المرور: ${error.toString()}',
      };
    }
  }

  /// تفعيل/إيقاف المستخدم
  Future<Map<String, dynamic>> toggleUserStatus({
    required String userId,
    required bool isActive,
  }) async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      debugPrint('🔄 ${isActive ? 'تفعيل' : 'إيقاف'} المستخدم: $userId');

      // تحديث حالة المستخدم في الجدول العام
      try {
        await _adminClient!
            .from('users')
            .update({'is_active': isActive})
            .eq('id', userId);
        debugPrint('✅ تم تحديث حالة المستخدم في قاعدة البيانات');
      } catch (e) {
        debugPrint('❌ خطأ في تحديث حالة المستخدم: $e');
        throw Exception('فشل في تحديث حالة المستخدم: $e');
      }

      // إيقاف الأجهزة إذا تم إيقاف المستخدم
      if (!isActive) {
        try {
          await _adminClient!
              .from('devices')
              .update({'is_active': false})
              .eq('user_id', userId);
          debugPrint('✅ تم إيقاف أجهزة المستخدم');
        } catch (e) {
          debugPrint('⚠️ خطأ في إيقاف أجهزة المستخدم: $e');
          // نتابع العملية حتى لو فشل إيقاف الأجهزة
        }
      }

      debugPrint('🎉 تم ${isActive ? 'تفعيل' : 'إيقاف'} المستخدم بنجاح');
      return {
        'success': true,
        'message': isActive ? 'تم تفعيل المستخدم' : 'تم إيقاف المستخدم',
      };
    } catch (error) {
      debugPrint('❌ خطأ عام في تغيير حالة المستخدم: $error');
      return {
        'success': false,
        'error': 'خطأ في تغيير حالة المستخدم: ${error.toString()}',
      };
    }
  }

  /// حذف المستخدم نهائياً
  Future<Map<String, dynamic>> deleteUser({
    required String userId,
  }) async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      debugPrint('🗑️ بدء عملية حذف المستخدم: $userId');

      // حذف الأجهزة المرتبطة أولاً
      try {
        await _adminClient!
            .from('devices')
            .delete()
            .eq('user_id', userId);
        debugPrint('✅ تم حذف الأجهزة المرتبطة');
      } catch (e) {
        debugPrint('⚠️ خطأ في حذف الأجهزة: $e');
        // نتابع العملية حتى لو فشل حذف الأجهزة
      }

      // حذف المستخدم من الجدول العام
      try {
        await _adminClient!
            .from('users')
            .delete()
            .eq('id', userId);
        debugPrint('✅ تم حذف المستخدم من جدول users');
      } catch (e) {
        debugPrint('❌ خطأ في حذف المستخدم من جدول users: $e');
        throw Exception('فشل في حذف المستخدم من قاعدة البيانات: $e');
      }

      // حذف المستخدم من نظام المصادقة
      try {
        await _adminClient!.auth.admin.deleteUser(userId);
        debugPrint('✅ تم حذف المستخدم من نظام المصادقة');
      } catch (e) {
        debugPrint('⚠️ خطأ في حذف المستخدم من نظام المصادقة: $e');
        // قد يكون المستخدم غير موجود في نظام المصادقة، نتابع العملية
      }

      debugPrint('🎉 تم حذف المستخدم بنجاح');
      return {
        'success': true,
        'message': 'تم حذف المستخدم نهائياً',
      };
    } catch (error) {
      debugPrint('❌ خطأ عام في حذف المستخدم: $error');
      return {
        'success': false,
        'error': 'خطأ في حذف المستخدم: ${error.toString()}',
      };
    }
  }

  /// فك ربط جهاز المستخدم
  Future<Map<String, dynamic>> unlinkDevice({
    required String userId,
  }) async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      await _adminClient!
          .from('devices')
          .delete()
          .eq('user_id', userId);

      return {
        'success': true,
        'message': 'تم فك ربط الجهاز بنجاح',
      };
    } catch (error) {
      return {
        'success': false,
        'error': 'خطأ في فك ربط الجهاز: ${error.toString()}',
      };
    }
  }

  /// جلب جميع المستخدمين مع تفاصيل الأجهزة
  Future<List<Map<String, dynamic>>> getAllUsersWithDevices() async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      final response = await _adminClient!
          .from('users')
          .select('''
            *,
            devices(count)
          ''')
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      debugPrint('خطأ في جلب المستخدمين: $error');
      return [];
    }
  }

  /// حذف مستخدم بالرقم الوطني (تنظيف)
  Future<Map<String, dynamic>> deleteUserByNationalId({
    required String nationalId,
  }) async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      debugPrint('🧹 بدء تنظيف المستخدم بالرقم الوطني: $nationalId');

      // البحث عن المستخدم بالرقم الوطني
      final existingUser = await _adminClient!
          .from('users')
          .select('id, national_id, full_name')
          .eq('national_id', nationalId)
          .maybeSingle();

      if (existingUser == null) {
        return {
          'success': false,
          'error': 'لا يوجد مستخدم بهذا الرقم الوطني',
        };
      }

      final userId = existingUser['id'] as String;
      debugPrint('🔍 تم العثور على المستخدم: ${existingUser['full_name']} (ID: $userId)');

      // حذف المستخدم باستخدام الدالة الموجودة
      return await deleteUser(userId: userId);

    } catch (error) {
      debugPrint('❌ خطأ في حذف المستخدم بالرقم الوطني: $error');
      return {
        'success': false,
        'error': 'خطأ في حذف المستخدم: ${error.toString()}',
      };
    }
  }

  /// تنظيف المستخدمين المحذوفين من نظام المصادقة
  Future<Map<String, dynamic>> cleanupAuthUsers() async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      debugPrint('🧹 بدء تنظيف المستخدمين المحذوفين من نظام المصادقة...');

      // جلب جميع المستخدمين من جدول users
      final dbUsers = await _adminClient!
          .from('users')
          .select('id, email');

      debugPrint('📊 عدد المستخدمين في قاعدة البيانات: ${dbUsers.length}');

      // ملاحظة: لا يمكن جلب جميع المستخدمين من auth.users مباشرة
      // لذلك سنعتمد على محاولة حذف المستخدمين عند إنشاء مستخدم جديد

      return {
        'success': true,
        'message': 'تم فحص قاعدة البيانات',
        'db_users_count': dbUsers.length,
      };
    } catch (error) {
      debugPrint('❌ خطأ في تنظيف المستخدمين: $error');
      return {
        'success': false,
        'error': 'خطأ في تنظيف المستخدمين: ${error.toString()}',
      };
    }
  }

  /// فحص صحة الاتصال الإداري
  Future<bool> testAdminConnection() async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      // اختبار بسيط لجلب عدد المستخدمين
      final response = await _adminClient!
          .from('users')
          .select('id')
          .count(CountOption.exact);

      debugPrint('✅ Admin connection test successful. Users count: ${response.count}');
      return true;
    } catch (error) {
      debugPrint('❌ Admin connection test failed: $error');
      return false;
    }
  }

  /// فحص وتنظيف قاعدة البيانات - إظهار الوضع الحقيقي
  Future<Map<String, dynamic>> checkDatabaseStatus() async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      debugPrint('🔍 فحص حالة قاعدة البيانات...');

      // 1. فحص جدول users
      final usersInDB = await _adminClient!
          .from('users')
          .select('id, national_id, email, full_name, is_active');

      debugPrint('📊 المستخدمون في جدول public.users: ${usersInDB.length}');
      for (var user in usersInDB) {
        debugPrint('  👤 ${user['full_name']} (${user['national_id']}) - ${user['email']} - نشط: ${user['is_active']}');
      }

      // 2. محاولة فحص auth.users (محدود)
      int authUsersCount = 0;

      // نحاول جلب المستخدمين من auth عبر محاولة إنشاء مستخدم وهمي
      try {
        // هذا سيفشل ولكن سيعطينا معلومات عن المستخدمين الموجودين
        await _adminClient!.auth.admin.createUser(
          AdminUserAttributes(
            email: 'test-check-${DateTime.now().millisecondsSinceEpoch}@moon-memory.com',
            password: 'temp123456',
          ),
        );
      } catch (e) {
        // نتجاهل الخطأ
      }

      return {
        'success': true,
        'users_in_db': usersInDB.length,
        'users_data': usersInDB,
        'auth_users_count': authUsersCount,
        'message': 'تم فحص قاعدة البيانات بنجاح',
      };

    } catch (error) {
      debugPrint('❌ خطأ في فحص قاعدة البيانات: $error');
      return {
        'success': false,
        'error': 'خطأ في فحص قاعدة البيانات: ${error.toString()}',
      };
    }
  }

  /// تنظيف قاعدة البيانات - حذف جميع المستخدمين
  Future<Map<String, dynamic>> cleanDatabase() async {
    try {
      if (!isInitialized) {
        await initialize();
      }

      debugPrint('🧹 بدء تنظيف قاعدة البيانات...');

      // 1. جلب جميع المستخدمين من جدول users
      final usersInDB = await _adminClient!
          .from('users')
          .select('id, national_id, email, full_name');

      debugPrint('📊 سيتم حذف ${usersInDB.length} مستخدم من قاعدة البيانات');

      int deletedFromAuth = 0;
      int deletedFromDB = 0;

      // 2. حذف كل مستخدم من auth.users و public.users
      for (var user in usersInDB) {
        try {
          // حذف من auth.users
          await _adminClient!.auth.admin.deleteUser(user['id']);
          deletedFromAuth++;
          debugPrint('✅ تم حذف ${user['full_name']} من نظام المصادقة');
        } catch (authError) {
          debugPrint('⚠️ لم يتم العثور على ${user['full_name']} في نظام المصادقة');
        }

        try {
          // حذف من public.users
          await _adminClient!
              .from('users')
              .delete()
              .eq('id', user['id']);
          deletedFromDB++;
          debugPrint('✅ تم حذف ${user['full_name']} من قاعدة البيانات');
        } catch (dbError) {
          debugPrint('❌ فشل في حذف ${user['full_name']} من قاعدة البيانات: $dbError');
        }
      }

      debugPrint('🎉 تم تنظيف قاعدة البيانات:');
      debugPrint('  📤 حُذف من نظام المصادقة: $deletedFromAuth');
      debugPrint('  📤 حُذف من قاعدة البيانات: $deletedFromDB');

      return {
        'success': true,
        'deleted_from_auth': deletedFromAuth,
        'deleted_from_db': deletedFromDB,
        'message': 'تم تنظيف قاعدة البيانات بنجاح',
      };

    } catch (error) {
      debugPrint('❌ خطأ في تنظيف قاعدة البيانات: $error');
      return {
        'success': false,
        'error': 'خطأ في تنظيف قاعدة البيانات: ${error.toString()}',
      };
    }
  }
}
