import 'package:supabase_flutter/supabase_flutter.dart';
import 'admin_supabase_service.dart';
import 'package:flutter/foundation.dart';

class UserProfileService {
  static final UserProfileService _instance = UserProfileService._internal();
  factory UserProfileService() => _instance;
  UserProfileService._internal();

  SupabaseClient get _supabase => AdminSupabaseService.instance.adminClient;

  // الحصول على بيانات المستخدم الحالي من قاعدة البيانات
  Future<Map<String, dynamic>?> getCurrentUserProfile() async {
    try {
      // التأكد من تهيئة الخدمة الإدارية
      if (!AdminSupabaseService.instance.isInitialized) {
        await AdminSupabaseService.instance.initialize();
      }

      debugPrint('🔍 البحث عن المستخدمين في قاعدة البيانات...');

      // أولاً: جلب جميع المستخدمين لنرى البيانات المتاحة
      final allUsers = await _supabase
          .from('users')
          .select('*')
          .limit(5);

      debugPrint('📊 عدد المستخدمين الموجودين: ${allUsers.length}');

      if (allUsers.isNotEmpty) {
        debugPrint('🔍 أعمدة الجدول المتاحة: ${allUsers.first.keys.toList()}');

        // عرض بيانات أول مستخدم
        final firstUser = allUsers.first;
        debugPrint('� أول مستخدم:');
        debugPrint('   - ID: ${firstUser['id']}');
        debugPrint('   - الاسم: ${firstUser['full_name']}');
        debugPrint('   - البريد: ${firstUser['email']}');
        debugPrint('   - الهاتف: ${firstUser['phone']}');
        debugPrint('   - تاريخ الإنشاء: ${firstUser['created_at']}');
        debugPrint('   - تاريخ التحديث: ${firstUser['updated_at']}');

        return firstUser;
      }

      debugPrint('❌ لا يوجد مستخدمون في قاعدة البيانات');

      // إنشاء بيانات افتراضية
      return {
        'id': 'guest_user',
        'full_name': 'مستخدم ضيف',
        'email': '<EMAIL>',
        'phone': '',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

    } catch (e) {
      debugPrint('❌ خطأ في الحصول على بيانات المستخدم: $e');
      return null;
    }
  }

  // تحديث بيانات المستخدم
  Future<bool> updateUserProfile({
    String? fullName,
    String? phone,
    String? avatarUrl,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      final updates = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (fullName != null) updates['full_name'] = fullName;
      if (phone != null) updates['phone'] = phone;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;

      await _supabase
          .from('users')
          .update(updates)
          .eq('id', user.id);

      debugPrint('✅ تم تحديث بيانات المستخدم بنجاح');
      return true;

    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات المستخدم: $e');
      return false;
    }
  }

  // إنشاء أو تحديث بيانات المستخدم
  Future<bool> upsertUserProfile(Map<String, dynamic> userData) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      final data = {
        'id': user.id,
        'full_name': userData['full_name'] ?? user.email?.split('@').first ?? 'مستخدم',
        'email': user.email ?? '',
        'phone': userData['phone'] ?? user.phone ?? '',
        'avatar_url': userData['avatar_url'] ?? user.userMetadata?['avatar_url'],
        'created_at': userData['created_at'] ?? user.createdAt,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase
          .from('users')
          .upsert(data);

      debugPrint('✅ تم حفظ بيانات المستخدم بنجاح');
      return true;

    } catch (e) {
      debugPrint('❌ خطأ في حفظ بيانات المستخدم: $e');
      return false;
    }
  }

  // التحقق من وجود المستخدم في قاعدة البيانات
  Future<bool> userExistsInDatabase() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      final response = await _supabase
          .from('users')
          .select('id')
          .eq('id', user.id)
          .maybeSingle();

      return response != null;

    } catch (e) {
      debugPrint('❌ خطأ في التحقق من وجود المستخدم: $e');
      return false;
    }
  }

  // الحصول على اسم المستخدم المختصر
  String getDisplayName(Map<String, dynamic>? userProfile) {
    if (userProfile == null) return 'مستخدم';
    
    final fullName = userProfile['full_name'] as String?;
    if (fullName != null && fullName.isNotEmpty) {
      return fullName;
    }
    
    final email = userProfile['email'] as String?;
    if (email != null && email.isNotEmpty) {
      return email.split('@').first;
    }
    
    return 'مستخدم';
  }

  // الحصول على الأحرف الأولى للاسم (للأفاتار)
  String getInitials(Map<String, dynamic>? userProfile) {
    final displayName = getDisplayName(userProfile);
    
    final words = displayName.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty) {
      return words[0].substring(0, 1).toUpperCase();
    }
    
    return 'U';
  }
}
