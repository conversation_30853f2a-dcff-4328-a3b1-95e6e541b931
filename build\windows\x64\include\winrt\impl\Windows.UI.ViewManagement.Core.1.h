// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_ViewManagement_Core_1_H
#define WINRT_Windows_UI_ViewManagement_Core_1_H
#include "winrt/impl/Windows.UI.ViewManagement.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::ViewManagement::Core
{
    struct __declspec(empty_bases) ICoreInputView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputView>
    {
        ICoreInputView(std::nullptr_t = nullptr) noexcept {}
        ICoreInputView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputView2>
    {
        ICoreInputView2(std::nullptr_t = nullptr) noexcept {}
        ICoreInputView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputView3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputView3>
    {
        ICoreInputView3(std::nullptr_t = nullptr) noexcept {}
        ICoreInputView3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputView4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputView4>
    {
        ICoreInputView4(std::nullptr_t = nullptr) noexcept {}
        ICoreInputView4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputViewHidingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputViewHidingEventArgs>
    {
        ICoreInputViewHidingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreInputViewHidingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputViewOcclusion :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputViewOcclusion>
    {
        ICoreInputViewOcclusion(std::nullptr_t = nullptr) noexcept {}
        ICoreInputViewOcclusion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputViewOcclusionsChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputViewOcclusionsChangedEventArgs>
    {
        ICoreInputViewOcclusionsChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreInputViewOcclusionsChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputViewShowingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputViewShowingEventArgs>
    {
        ICoreInputViewShowingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreInputViewShowingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputViewStatics>
    {
        ICoreInputViewStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreInputViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputViewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputViewStatics2>
    {
        ICoreInputViewStatics2(std::nullptr_t = nullptr) noexcept {}
        ICoreInputViewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreInputViewTransferringXYFocusEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreInputViewTransferringXYFocusEventArgs>
    {
        ICoreInputViewTransferringXYFocusEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreInputViewTransferringXYFocusEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUISettingsController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettingsController>
    {
        IUISettingsController(std::nullptr_t = nullptr) noexcept {}
        IUISettingsController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUISettingsControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettingsControllerStatics>
    {
        IUISettingsControllerStatics(std::nullptr_t = nullptr) noexcept {}
        IUISettingsControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
