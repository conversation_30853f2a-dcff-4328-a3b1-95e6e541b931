// 📱 خدمة تتبع الجلسات لتطبيق الكاميرا
// يجب إضافة هذا الكود إلى تطبيق الكاميرا

import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

class CameraSessionService {
  static CameraSessionService? _instance;
  static CameraSessionService get instance => _instance ??= CameraSessionService._();
  
  CameraSessionService._();
  
  Timer? _heartbeatTimer;
  String? _currentSessionId;
  String? _currentUserId;
  bool _isSessionActive = false;
  
  SupabaseClient get _supabase => Supabase.instance.client;
  
  /// بدء جلسة جديدة عند تسجيل الدخول
  Future<void> startSession({
    required String userId,
    required String deviceName,
    required String deviceModel,
    required String deviceBrand,
    String? locationName,
    double? latitude,
    double? longitude,
  }) async {
    try {
      debugPrint('🚀 بدء جلسة جديدة للمستخدم: $userId');
      
      _currentUserId = userId;
      
      // 1. تحديث حالة المستخدم
      await _supabase.from('users').update({
        'last_seen': DateTime.now().toIso8601String(),
        'is_online': true,
        'last_location_name': locationName,
        'last_location_lat': latitude,
        'last_location_lng': longitude,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', userId);
      
      // 2. تسجيل/تحديث الجهاز
      await _registerDevice(
        userId: userId,
        deviceName: deviceName,
        deviceModel: deviceModel,
        deviceBrand: deviceBrand,
      );
      
      // 3. إنشاء جلسة جديدة (إذا كان جدول user_sessions موجود)
      try {
        final sessionResponse = await _supabase.from('user_sessions').insert({
          'user_id': userId,
          'session_start': DateTime.now().toIso8601String(),
          'last_activity': DateTime.now().toIso8601String(),
          'is_active': true,
          'location_name': locationName,
          'latitude': latitude,
          'longitude': longitude,
        }).select().single();
        
        _currentSessionId = sessionResponse['id'];
        
        // تحديث current_session_id في جدول users
        await _supabase.from('users').update({
          'current_session_id': _currentSessionId,
        }).eq('id', userId);
        
      } catch (e) {
        debugPrint('⚠️ جدول user_sessions غير موجود أو خطأ في إنشاء الجلسة: $e');
      }
      
      // 4. بدء heartbeat كل دقيقة
      _startHeartbeat();
      _isSessionActive = true;
      
      debugPrint('✅ تم بدء الجلسة بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في بدء الجلسة: $e');
    }
  }
  
  /// تسجيل/تحديث الجهاز
  Future<void> _registerDevice({
    required String userId,
    required String deviceName,
    required String deviceModel,
    required String deviceBrand,
  }) async {
    try {
      // البحث عن الجهاز الموجود
      final existingDevice = await _supabase
          .from('devices')
          .select()
          .eq('user_id', userId)
          .eq('device_name', deviceName)
          .maybeSingle();
      
      if (existingDevice != null) {
        // تحديث الجهاز الموجود
        await _supabase.from('devices').update({
          'last_login': DateTime.now().toIso8601String(),
          'is_active': true,
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('id', existingDevice['id']);
        
        debugPrint('🔄 تم تحديث الجهاز الموجود: $deviceName');
      } else {
        // إنشاء جهاز جديد
        await _supabase.from('devices').insert({
          'user_id': userId,
          'device_name': deviceName,
          'device_model': deviceModel,
          'device_brand': deviceBrand,
          'last_login': DateTime.now().toIso8601String(),
          'is_active': true,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
        
        debugPrint('✅ تم تسجيل جهاز جديد: $deviceName');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل الجهاز: $e');
    }
  }
  
  /// بدء heartbeat لتحديث حالة الاتصال كل دقيقة
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _updateActivity();
    });
    debugPrint('💓 تم بدء heartbeat كل دقيقة');
  }
  
  /// تحديث آخر نشاط
  Future<void> _updateActivity() async {
    if (!_isSessionActive || _currentUserId == null) return;
    
    try {
      // تحديث last_seen في جدول users
      await _supabase.from('users').update({
        'last_seen': DateTime.now().toIso8601String(),
        'is_online': true,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', _currentUserId!);
      
      // تحديث الجلسة إذا كانت موجودة
      if (_currentSessionId != null) {
        try {
          await _supabase.from('user_sessions').update({
            'last_activity': DateTime.now().toIso8601String(),
          }).eq('id', _currentSessionId!);
        } catch (e) {
          debugPrint('⚠️ خطأ في تحديث الجلسة: $e');
        }
      }
      
      // تسجيل النشاط (إذا كان جدول user_activity_log موجود)
      try {
        await _supabase.from('user_activity_log').insert({
          'user_id': _currentUserId!,
          'activity_type': 'heartbeat',
          'timestamp': DateTime.now().toIso8601String(),
        });
      } catch (e) {
        debugPrint('⚠️ جدول user_activity_log غير موجود: $e');
      }
      
      debugPrint('💓 تم تحديث النشاط للمستخدم: $_currentUserId');
      
    } catch (e) {
      debugPrint('❌ خطأ في تحديث النشاط: $e');
    }
  }
  
  /// تسجيل نشاط معين (التقاط صورة، فيديو، إلخ)
  Future<void> logActivity(String activityType, {Map<String, dynamic>? metadata}) async {
    if (_currentUserId == null) return;
    
    try {
      // تحديث last_seen
      await _supabase.from('users').update({
        'last_seen': DateTime.now().toIso8601String(),
        'is_online': true,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', _currentUserId!);
      
      // تسجيل النشاط المحدد
      try {
        await _supabase.from('user_activity_log').insert({
          'user_id': _currentUserId!,
          'activity_type': activityType,
          'timestamp': DateTime.now().toIso8601String(),
          'metadata': metadata,
        });
      } catch (e) {
        debugPrint('⚠️ جدول user_activity_log غير موجود: $e');
      }
      
      debugPrint('📝 تم تسجيل النشاط: $activityType');
      
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل النشاط: $e');
    }
  }
  
  /// إنهاء الجلسة عند تسجيل الخروج أو إغلاق التطبيق
  Future<void> endSession() async {
    try {
      debugPrint('🛑 إنهاء الجلسة...');
      
      _heartbeatTimer?.cancel();
      _isSessionActive = false;
      
      if (_currentUserId != null) {
        // تحديث حالة المستخدم
        await _supabase.from('users').update({
          'is_online': false,
          'current_session_id': null,
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('id', _currentUserId!);
        
        // إنهاء الجلسة
        if (_currentSessionId != null) {
          try {
            await _supabase.from('user_sessions').update({
              'session_end': DateTime.now().toIso8601String(),
              'is_active': false,
            }).eq('id', _currentSessionId!);
          } catch (e) {
            debugPrint('⚠️ خطأ في إنهاء الجلسة: $e');
          }
        }
        
        // تسجيل نشاط الخروج
        await logActivity('logout');
      }
      
      _currentUserId = null;
      _currentSessionId = null;
      
      debugPrint('✅ تم إنهاء الجلسة بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في إنهاء الجلسة: $e');
    }
  }
  
  /// التحقق من حالة الجلسة
  bool get isSessionActive => _isSessionActive;
  String? get currentUserId => _currentUserId;
  String? get currentSessionId => _currentSessionId;
}

// 📱 كيفية الاستخدام في تطبيق الكاميرا:
/*

// 1. عند تسجيل الدخول:
await CameraSessionService.instance.startSession(
  userId: user.id,
  deviceName: 'My Phone',
  deviceModel: 'iPhone 13',
  deviceBrand: 'Apple',
  locationName: 'الرياض، السعودية',
  latitude: 24.7136,
  longitude: 46.6753,
);

// 2. عند التقاط صورة:
await CameraSessionService.instance.logActivity('photo_capture', metadata: {
  'photo_id': photoId,
  'location': currentLocation,
});

// 3. عند رفع فيديو:
await CameraSessionService.instance.logActivity('video_upload', metadata: {
  'video_id': videoId,
  'duration': videoDuration,
});

// 4. عند تسجيل الخروج أو إغلاق التطبيق:
await CameraSessionService.instance.endSession();

*/
