-- ===================================
-- إنشاء نظام تتبع المستخدمين المتصلين
-- بناءً على بنية قاعدة البيانات الحقيقية
-- ===================================

SELECT 'بدء إنشاء نظام تتبع المستخدمين المتصلين...' as status;

-- ===================================
-- 1. جدول جلسات المستخدمين
-- ===================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    
    -- معلومات الجلسة
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    
    -- معلومات الموقع (من تطبيق الكاميرا)
    location_lat DOUBLE PRECISION,
    location_lng DOUBLE PRECISION,
    location_name TEXT,
    
    -- معلومات إضافية
    app_version TEXT,
    ip_address INET,
    
    -- طوابع زمنية
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================
-- 2. جدول سجل نشاط المستخدمين
-- ===================================

CREATE TABLE IF NOT EXISTS user_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    
    -- نوع النشاط
    activity_type TEXT NOT NULL CHECK (activity_type IN (
        'login', 'logout', 'heartbeat', 'app_open', 'app_close',
        'photo_taken', 'video_recorded', 'photo_uploaded', 'video_uploaded',
        'location_update', 'device_verified'
    )),
    
    -- بيانات النشاط
    activity_data JSONB DEFAULT '{}',
    
    -- معلومات الموقع والوقت
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    location_lat DOUBLE PRECISION,
    location_lng DOUBLE PRECISION,
    
    -- معلومات إضافية
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================
-- 3. تحديث جدول المستخدمين الموجود
-- ===================================

-- إضافة أعمدة جديدة لتتبع الحالة
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS is_online BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS current_session_id UUID,
ADD COLUMN IF NOT EXISTS total_sessions INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_location_lat DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS last_location_lng DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS last_location_name TEXT;

-- إضافة Foreign Key للجلسة الحالية
ALTER TABLE users 
ADD CONSTRAINT fk_users_current_session 
FOREIGN KEY (current_session_id) REFERENCES user_sessions(id) ON DELETE SET NULL;

-- ===================================
-- 4. إنشاء الفهارس للأداء
-- ===================================

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);

CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_session_id ON user_activity_log(session_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_activity_type ON user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_activity_log_timestamp ON user_activity_log(timestamp);

CREATE INDEX IF NOT EXISTS idx_users_is_online ON users(is_online);
CREATE INDEX IF NOT EXISTS idx_users_last_seen ON users(last_seen);

-- ===================================
-- 5. دوال مساعدة
-- ===================================

-- دالة لتحديث آخر نشاط المستخدم
CREATE OR REPLACE FUNCTION update_user_last_activity(
    p_user_id UUID,
    p_session_id UUID DEFAULT NULL,
    p_location_lat DOUBLE PRECISION DEFAULT NULL,
    p_location_lng DOUBLE PRECISION DEFAULT NULL,
    p_location_name TEXT DEFAULT NULL
) RETURNS void AS $$
BEGIN
    UPDATE users 
    SET 
        last_seen = NOW(),
        is_online = true,
        current_session_id = COALESCE(p_session_id, current_session_id),
        last_location_lat = COALESCE(p_location_lat, last_location_lat),
        last_location_lng = COALESCE(p_location_lng, last_location_lng),
        last_location_name = COALESCE(p_location_name, last_location_name)
    WHERE id = p_user_id;
    
    IF p_session_id IS NOT NULL THEN
        UPDATE user_sessions 
        SET 
            last_activity = NOW(),
            updated_at = NOW(),
            location_lat = COALESCE(p_location_lat, location_lat),
            location_lng = COALESCE(p_location_lng, location_lng),
            location_name = COALESCE(p_location_name, location_name)
        WHERE id = p_session_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على المستخدمين المتصلين
CREATE OR REPLACE FUNCTION get_online_users(
    p_minutes_threshold INTEGER DEFAULT 5
) RETURNS TABLE (
    user_id UUID,
    full_name VARCHAR(255),
    email VARCHAR(255),
    department VARCHAR(100),
    last_seen TIMESTAMP WITH TIME ZONE,
    session_id UUID,
    device_name TEXT,
    device_model TEXT,
    device_brand TEXT,
    last_device_login TIMESTAMP WITH TIME ZONE,
    location_name TEXT,
    minutes_since_last_seen DOUBLE PRECISION
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.full_name,
        u.email,
        u.department,
        u.last_seen,
        s.id,
        d.device_name,
        d.device_model,
        d.device_brand,
        d.last_login,
        COALESCE(s.location_name, u.last_location_name),
        CAST(EXTRACT(EPOCH FROM (NOW() - u.last_seen))/60 AS DOUBLE PRECISION)
    FROM users u
    LEFT JOIN user_sessions s ON u.current_session_id = s.id
    LEFT JOIN devices d ON s.device_id = d.id AND s.device_id IS NOT NULL
    WHERE 
        u.is_online = true 
        AND u.is_active = true
        AND u.last_seen > NOW() - INTERVAL '1 minute' * p_minutes_threshold
    ORDER BY u.last_seen DESC;
END;
$$ LANGUAGE plpgsql;

-- دالة لبدء جلسة جديدة
CREATE OR REPLACE FUNCTION start_user_session(
    p_user_id UUID,
    p_device_id UUID,
    p_app_version TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_location_lat DOUBLE PRECISION DEFAULT NULL,
    p_location_lng DOUBLE PRECISION DEFAULT NULL,
    p_location_name TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_session_id UUID;
BEGIN
    INSERT INTO user_sessions (
        user_id, device_id, app_version, ip_address,
        location_lat, location_lng, location_name
    ) VALUES (
        p_user_id, p_device_id, p_app_version, p_ip_address,
        p_location_lat, p_location_lng, p_location_name
    ) RETURNING id INTO v_session_id;

    UPDATE users
    SET
        is_online = true,
        current_session_id = v_session_id,
        last_seen = NOW(),
        total_sessions = total_sessions + 1,
        last_location_lat = COALESCE(p_location_lat, last_location_lat),
        last_location_lng = COALESCE(p_location_lng, last_location_lng),
        last_location_name = COALESCE(p_location_name, last_location_name)
    WHERE id = p_user_id;

    INSERT INTO user_activity_log (user_id, session_id, activity_type, location_lat, location_lng)
    VALUES (p_user_id, v_session_id, 'login', p_location_lat, p_location_lng);

    RETURN v_session_id;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنهاء الجلسة
CREATE OR REPLACE FUNCTION end_user_session(p_session_id UUID) RETURNS void AS $$
DECLARE v_user_id UUID;
BEGIN
    SELECT user_id INTO v_user_id FROM user_sessions WHERE id = p_session_id;
    UPDATE user_sessions SET session_end = NOW(), is_active = false, updated_at = NOW() WHERE id = p_session_id;
    UPDATE users SET is_online = false, current_session_id = NULL WHERE id = v_user_id;
    INSERT INTO user_activity_log (user_id, session_id, activity_type) VALUES (v_user_id, p_session_id, 'logout');
END;
$$ LANGUAGE plpgsql;

-- إعداد الصلاحيات
ALTER TABLE user_sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_log DISABLE ROW LEVEL SECURITY;
GRANT ALL PRIVILEGES ON user_sessions TO authenticated;
GRANT ALL PRIVILEGES ON user_sessions TO service_role;
GRANT ALL PRIVILEGES ON user_activity_log TO authenticated;
GRANT ALL PRIVILEGES ON user_activity_log TO service_role;

-- View للمستخدمين المتصلين
CREATE OR REPLACE VIEW v_online_users AS
SELECT
    u.id, u.national_id, u.full_name, u.email, u.department, u.position, u.last_seen, u.is_online,
    s.id as session_id, d.device_name, d.device_model, d.device_brand, d.last_login as device_last_login,
    COALESCE(s.location_name, u.last_location_name) as location_name,
    EXTRACT(EPOCH FROM (NOW() - u.last_seen))/60 as minutes_since_last_seen,
    CASE
        WHEN u.last_seen > NOW() - INTERVAL '2 minutes' THEN 'متصل الآن'
        WHEN u.last_seen > NOW() - INTERVAL '5 minutes' THEN 'نشط مؤخراً'
        ELSE 'غير متصل'
    END as status_text
FROM users u
LEFT JOIN user_sessions s ON u.current_session_id = s.id
LEFT JOIN devices d ON s.device_id = d.id AND s.device_id IS NOT NULL
WHERE u.is_active = true
ORDER BY u.last_seen DESC;

-- تحديث البيانات الموجودة
UPDATE users
SET
    last_seen = CASE
        WHEN is_active = true THEN COALESCE(last_login, created_at)
        ELSE created_at
    END,
    is_online = false,
    total_sessions = 0
WHERE last_seen IS NULL;

-- فحص النتائج
SELECT 'تم إنشاء الجداول:' as info;
SELECT table_name,
    CASE
        WHEN table_name IN ('user_sessions', 'user_activity_log') THEN '✅ جديد'
        WHEN table_name = 'users' THEN '🔄 محدث'
        ELSE '📋 موجود'
    END as status
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'user_sessions', 'user_activity_log', 'devices')
ORDER BY table_name;

SELECT 'إحصائيات سريعة:' as info;
SELECT 'إجمالي المستخدمين' as metric, COUNT(*) as value FROM users
UNION ALL SELECT 'المستخدمين النشطين', COUNT(*) FROM users WHERE is_active = true
UNION ALL SELECT 'المستخدمين المتصلين', COUNT(*) FROM users WHERE is_online = true
UNION ALL SELECT 'إجمالي الأجهزة', COUNT(*) FROM devices
UNION ALL SELECT 'الأجهزة النشطة', COUNT(*) FROM devices WHERE is_active = true;

SELECT '🎉 تم إنشاء نظام تتبع المستخدمين المتصلين بنجاح!' as status;
