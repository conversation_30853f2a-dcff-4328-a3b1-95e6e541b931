import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_colors.dart';
import '../providers/users_provider.dart';
import '../providers/admin_service_provider.dart';
import '../widgets/users/users_filter_bar.dart';
import '../widgets/users/users_data_table.dart';
import '../widgets/users/users_stats_bar.dart';


class UsersScreen extends ConsumerWidget {
  const UsersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersState = ref.watch(usersProvider);

    return Column(
      children: [
        // أزرار الإجراءات
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          child: Row(
            children: [
              const Spacer(),
              OutlinedButton.icon(
                onPressed: () {
                  ref.read(usersProvider.notifier).refresh();
                },
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('تحديث القائمة', style: TextStyle(fontSize: 12)),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  minimumSize: Size.zero,
                ),
              ),
            ],
          ),
        ),
            
            // شريط الإحصائيات
            const UsersStatsBar(),



            // شريط الفلترة
            const UsersFilterBar(),
            
            // جدول المستخدمين
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                child: Card(
                  elevation: 2,
                  child: Column(
                    children: [
                      // رأس الجدول
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppColors.surfaceBackground,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              'قائمة المستخدمين',
                              style: const TextStyle(
                                color: AppColors.primaryGold,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            const Spacer(),

                            // أزرار إدارة قاعدة البيانات
                            ElevatedButton.icon(
                              onPressed: () => _checkDatabase(context, ref),
                              icon: const Icon(Icons.search, size: 16),
                              label: const Text('فحص قاعدة البيانات'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.info,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                textStyle: const TextStyle(fontSize: 12),
                              ),
                            ),
                            const SizedBox(width: 8),

                            ElevatedButton.icon(
                              onPressed: () => _cleanDatabase(context, ref),
                              icon: const Icon(Icons.cleaning_services, size: 16),
                              label: const Text('تنظيف قاعدة البيانات'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.warning,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                textStyle: const TextStyle(fontSize: 12),
                              ),
                            ),
                            const SizedBox(width: 16),

                            if (usersState.isLoading)
                              const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppColors.primaryGold,
                                ),
                              ),
                            const SizedBox(width: 16),
                            Text(
                              '${usersState.totalCount} مستخدم',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.secondaryText,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // محتوى الجدول
                      Expanded(
                        child: usersState.error != null
                            ? _buildErrorState(context, usersState.error!, ref)
                            : const UsersDataTable(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
  }

  Widget _buildErrorState(BuildContext context, String error, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل المستخدمين',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(usersProvider.notifier).refresh();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// فحص حالة قاعدة البيانات
  void _checkDatabase(BuildContext context, WidgetRef ref) async {
    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري فحص قاعدة البيانات...'),
          ],
        ),
      ),
    );

    try {
      final result = await ref.read(adminSupabaseServiceProvider).checkDatabaseStatus();

      // إغلاق مؤشر التحميل
      if (context.mounted) Navigator.of(context).pop();

      if (result['success']) {
        final usersData = result['users_data'] as List;

        // إظهار نتائج الفحص
        if (context.mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('نتائج فحص قاعدة البيانات'),
              content: SizedBox(
                width: 500,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('📊 عدد المستخدمين في قاعدة البيانات: ${result['users_in_db']}'),
                    const SizedBox(height: 16),
                    if (usersData.isNotEmpty) ...[
                      const Text('👥 المستخدمون الموجودون:'),
                      const SizedBox(height: 8),
                      ...usersData.map((user) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text('• ${user['full_name']} (${user['national_id']}) - ${user['email']}'),
                      )),
                    ] else ...[
                      const Text('✅ قاعدة البيانات فارغة - لا توجد مستخدمون'),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إغلاق'),
                ),
              ],
            ),
          );
        }
      } else {
        // إظهار رسالة خطأ
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في فحص قاعدة البيانات: ${result['error']}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (error) {
      // إغلاق مؤشر التحميل
      if (context.mounted) Navigator.of(context).pop();

      // إظهار رسالة خطأ
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فحص قاعدة البيانات: ${error.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// تنظيف قاعدة البيانات
  void _cleanDatabase(BuildContext context, WidgetRef ref) async {
    // تأكيد الحذف
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تنظيف قاعدة البيانات'),
        content: const Text(
          '⚠️ هذا الإجراء سيحذف جميع المستخدمين من قاعدة البيانات ونظام المصادقة.\n\n'
          'هل أنت متأكد من أنك تريد المتابعة؟'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('نعم، احذف الكل'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // إظهار مؤشر التحميل
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تنظيف قاعدة البيانات...'),
            ],
          ),
        ),
      );
    }

    try {
      final result = await ref.read(adminSupabaseServiceProvider).cleanDatabase();

      // إغلاق مؤشر التحميل
      if (context.mounted) Navigator.of(context).pop();

      if (result['success']) {
        // إعادة تحميل المستخدمين
        ref.read(usersProvider.notifier).refresh();

        // إظهار رسالة نجاح
        if (context.mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('تم تنظيف قاعدة البيانات'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('✅ تم حذف ${result['deleted_from_auth']} مستخدم من نظام المصادقة'),
                  Text('✅ تم حذف ${result['deleted_from_db']} مستخدم من قاعدة البيانات'),
                  const SizedBox(height: 16),
                  const Text('🎉 قاعدة البيانات نظيفة الآن ويمكنك إنشاء مستخدمين جدد'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إغلاق'),
                ),
              ],
            ),
          );
        }
      } else {
        // إظهار رسالة خطأ
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تنظيف قاعدة البيانات: ${result['error']}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (error) {
      // إغلاق مؤشر التحميل
      if (context.mounted) Navigator.of(context).pop();

      // إظهار رسالة خطأ
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تنظيف قاعدة البيانات: ${error.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
