﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{*************-395A-931E-CCF3F03718FE}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>moon_memory_admin</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moon_memory_admin.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moon_memory_admin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">moon_memory_admin.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">moon_memory_admin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moon_memory_admin.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moon_memory_admin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\app_links\Debug\app_links_plugin.lib;..\plugins\geolocator_windows\Debug\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Debug\permission_handler_windows_plugin.lib;..\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.lib;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/Debug/moon_memory_admin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/Debug/moon_memory_admin.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\app_links\Profile\app_links_plugin.lib;..\plugins\geolocator_windows\Profile\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Profile\permission_handler_windows_plugin.lib;..\plugins\url_launcher_windows\Profile\url_launcher_windows_plugin.lib;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/Profile/moon_memory_admin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/Profile/moon_memory_admin.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\app_links\Release\app_links_plugin.lib;..\plugins\geolocator_windows\Release\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.lib;..\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.lib;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/Release/moon_memory_admin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/Release/moon_memory_admin.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\CMakeLists.txt">
      <StdOutEncoding>UTF-8</StdOutEncoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\flutter_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\utils.cpp" />
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\win32_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{C5825266-62BB-3A53-A41E-0C81E34DD81F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\app_links\app_links_plugin.vcxproj">
      <Project>{67199484-63A5-3434-B1D2-82032A5B1E29}</Project>
      <Name>app_links_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{5E1FD7E4-6392-393B-B19E-4E914843B1FE}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\geolocator_windows\geolocator_windows_plugin.vcxproj">
      <Project>{DD231E01-8187-3065-8C4C-47B8E5E30CDE}</Project>
      <Name>geolocator_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\permission_handler_windows_plugin.vcxproj">
      <Project>{F9D91351-2C5C-3A24-A38C-B1664CF29155}</Project>
      <Name>permission_handler_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\url_launcher_windows\url_launcher_windows_plugin.vcxproj">
      <Project>{93EDD56B-EC8C-32F3-8455-3C35D41865FD}</Project>
      <Name>url_launcher_windows_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>