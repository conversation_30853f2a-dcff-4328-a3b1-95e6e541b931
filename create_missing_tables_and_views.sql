-- 🛠️ إنشاء الجداول والـ Views المفقودة لنظام تتبع المستخدمين المتصلين

-- ===================================
-- 1. إنشاء جدول user_sessions
-- ===================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    device_id UUID REFERENCES devices(id),
    location_name TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);

-- ===================================
-- 2. إنشاء جدول user_activity_log
-- ===================================

CREATE TABLE IF NOT EXISTS user_activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    activity_type VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB,
    location_name TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_timestamp ON user_activity_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON user_activity_log(activity_type);

-- ===================================
-- 3. إنشاء View للمستخدمين المتصلين المبسط
-- ===================================

CREATE OR REPLACE VIEW v_simple_online_users AS
SELECT
    u.id,
    u.national_id,
    u.full_name,
    u.email,
    u.department,
    u.position,
    u.last_seen,
    u.is_online,
    u.is_active,
    u.current_session_id,
    u.last_location_name,
    u.last_location_lat,
    u.last_location_lng,
    
    -- حساب الدقائق منذ آخر ظهور
    EXTRACT(EPOCH FROM (NOW() - u.last_seen))/60 as minutes_since_last_seen,
    
    -- تحديد حالة النص
    CASE
        WHEN u.last_seen > NOW() - INTERVAL '2 minutes' THEN 'متصل الآن'
        WHEN u.last_seen > NOW() - INTERVAL '5 minutes' THEN 'نشط مؤخراً'
        WHEN u.last_seen > NOW() - INTERVAL '30 minutes' THEN 'غير نشط'
        ELSE 'غير متصل'
    END as status_text,
    
    -- بيانات الجهاز (إذا كان متاح)
    d.device_name,
    d.device_model,
    d.device_brand,
    d.last_login as device_last_login,
    
    -- بيانات الجلسة (إذا كانت متاحة)
    s.session_start,
    s.last_activity as session_last_activity,
    s.location_name as session_location
    
FROM users u
LEFT JOIN user_sessions s ON u.current_session_id = s.id AND s.is_active = true
LEFT JOIN devices d ON s.device_id = d.id
WHERE u.is_active = true
ORDER BY u.last_seen DESC NULLS LAST;

-- ===================================
-- 4. إنشاء View تفصيلي للمستخدمين المتصلين
-- ===================================

CREATE OR REPLACE VIEW v_online_users AS
SELECT
    u.id,
    u.national_id,
    u.full_name,
    u.email,
    u.department,
    u.position,
    u.last_seen,
    u.is_online,
    u.is_active,
    
    -- إحصائيات الجلسة
    s.id as session_id,
    s.session_start,
    s.last_activity,
    s.location_name,
    s.latitude,
    s.longitude,
    
    -- بيانات الجهاز
    d.device_name,
    d.device_model,
    d.device_brand,
    d.last_login as device_last_login,
    
    -- حسابات زمنية
    EXTRACT(EPOCH FROM (NOW() - u.last_seen))/60 as minutes_since_last_seen,
    EXTRACT(EPOCH FROM (NOW() - s.last_activity))/60 as minutes_since_last_activity,
    EXTRACT(EPOCH FROM (s.last_activity - s.session_start))/60 as session_duration_minutes,
    
    -- حالة مفصلة
    CASE
        WHEN u.last_seen > NOW() - INTERVAL '1 minute' THEN 'متصل الآن'
        WHEN u.last_seen > NOW() - INTERVAL '2 minutes' THEN 'نشط جداً'
        WHEN u.last_seen > NOW() - INTERVAL '5 minutes' THEN 'نشط مؤخراً'
        WHEN u.last_seen > NOW() - INTERVAL '15 minutes' THEN 'غير نشط'
        WHEN u.last_seen > NOW() - INTERVAL '30 minutes' THEN 'خامل'
        ELSE 'غير متصل'
    END as detailed_status,
    
    -- عدد الأنشطة اليوم
    (SELECT COUNT(*) FROM user_activity_log a 
     WHERE a.user_id = u.id 
     AND a.timestamp >= CURRENT_DATE) as activities_today
    
FROM users u
LEFT JOIN user_sessions s ON u.current_session_id = s.id
LEFT JOIN devices d ON s.device_id = d.id
WHERE u.is_active = true
ORDER BY u.last_seen DESC NULLS LAST;

-- ===================================
-- 5. إنشاء دوال مساعدة
-- ===================================

-- دالة للحصول على المستخدمين المتصلين
CREATE OR REPLACE FUNCTION get_online_users_count(
    p_minutes_threshold INTEGER DEFAULT 2
) RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM users
        WHERE is_active = true
        AND last_seen > NOW() - (p_minutes_threshold || ' minutes')::INTERVAL
    );
END;
$$ LANGUAGE plpgsql;

-- دالة لتنظيف الجلسات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_sessions() RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    -- إنهاء الجلسات غير النشطة (أكثر من ساعة)
    UPDATE user_sessions 
    SET 
        is_active = false,
        session_end = NOW(),
        updated_at = NOW()
    WHERE is_active = true 
    AND last_activity < NOW() - INTERVAL '1 hour';
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    -- تحديث حالة المستخدمين
    UPDATE users 
    SET 
        is_online = false,
        current_session_id = NULL,
        updated_at = NOW()
    WHERE is_online = true 
    AND last_seen < NOW() - INTERVAL '5 minutes';
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 6. إنشاء Triggers لتحديث updated_at تلقائياً
-- ===================================

-- Trigger لجدول user_sessions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_sessions_updated_at
    BEFORE UPDATE ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===================================
-- 7. إنشاء RLS (Row Level Security) policies
-- ===================================

-- تفعيل RLS على الجداول الجديدة
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_log ENABLE ROW LEVEL SECURITY;

-- سياسة للمستخدمين المصرح لهم
CREATE POLICY "Users can view their own sessions" ON user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sessions" ON user_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sessions" ON user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسة للمديرين (يمكنهم رؤية كل شيء)
CREATE POLICY "Admins can view all sessions" ON user_sessions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND is_admin = true
        )
    );

-- نفس الشيء لجدول user_activity_log
CREATE POLICY "Users can view their own activities" ON user_activity_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activities" ON user_activity_log
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all activities" ON user_activity_log
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND is_admin = true
        )
    );

-- ===================================
-- 8. اختبار النتائج
-- ===================================

-- عرض الجداول المنشأة
SELECT 'تم إنشاء الجداول التالية:' as status;
SELECT table_name, table_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('user_sessions', 'user_activity_log')
ORDER BY table_name;

-- عرض الـ Views المنشأة
SELECT 'تم إنشاء الـ Views التالية:' as status;
SELECT table_name as view_name
FROM information_schema.views
WHERE table_schema = 'public'
AND table_name IN ('v_simple_online_users', 'v_online_users')
ORDER BY table_name;

-- اختبار الدوال
SELECT 'عدد المستخدمين المتصلين حالياً:' as test, get_online_users_count() as count;

-- عرض المستخدمين من الـ View الجديد
SELECT 'المستخدمين من v_simple_online_users:' as test;
SELECT full_name, email, status_text, minutes_since_last_seen
FROM v_simple_online_users
LIMIT 5;

SELECT '✅ تم إنشاء جميع الجداول والـ Views بنجاح!' as final_status;
