import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/devices_provider.dart';
import 'create_device_dialog.dart';

class DevicesFilterBar extends ConsumerStatefulWidget {
  const DevicesFilterBar({super.key});

  @override
  ConsumerState<DevicesFilterBar> createState() => _DevicesFilterBarState();
}

class _DevicesFilterBarState extends ConsumerState<DevicesFilterBar> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedStatus;
  String? _selectedType;
  String? _selectedUser;
  String? _selectedLocation;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: Card(
        elevation: 1,
        color: AppColors.cardBackground,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  // حقل البحث
                  Expanded(
                    flex: 3,
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'البحث في الأجهزة (الاسم، المعرف، النوع...)',
                        prefixIcon: const Icon(Icons.search, color: AppColors.primaryGold),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  ref.read(devicesProvider.notifier).searchDevices('');
                                },
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: AppColors.primaryGold.withValues(alpha: 0.3)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: AppColors.primaryGold.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: AppColors.primaryGold),
                        ),
                        filled: true,
                        fillColor: AppColors.surfaceBackground,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      style: const TextStyle(color: AppColors.primaryText),
                      onChanged: (value) {
                        setState(() {});
                        // تأخير البحث لتحسين الأداء
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (_searchController.text == value) {
                            ref.read(devicesProvider.notifier).searchDevices(value);
                          }
                        });
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // فلتر الحالة
                  SizedBox(
                    width: 140,
                    child: DropdownButtonFormField<String>(
                      value: _selectedStatus,
                      decoration: InputDecoration(
                        labelText: 'الحالة',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('جميع الحالات')),
                        DropdownMenuItem(value: 'active', child: Text('نشط')),
                        DropdownMenuItem(value: 'inactive', child: Text('متوقف')),
                        DropdownMenuItem(value: 'maintenance', child: Text('صيانة')),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedStatus = value);
                        ref.read(devicesProvider.notifier).filterByStatus(value);
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // فلتر النوع
                  SizedBox(
                    width: 140,
                    child: DropdownButtonFormField<String>(
                      value: _selectedType,
                      decoration: InputDecoration(
                        labelText: 'النوع',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('جميع الأنواع')),
                        DropdownMenuItem(value: 'camera', child: Text('كاميرا')),
                        DropdownMenuItem(value: 'sensor', child: Text('مستشعر')),
                        DropdownMenuItem(value: 'recorder', child: Text('مسجل')),
                        DropdownMenuItem(value: 'tracker', child: Text('متتبع')),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedType = value);
                        ref.read(devicesProvider.notifier).filterByType(value);
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر مسح الفلاتر
                  OutlinedButton.icon(
                    onPressed: _hasActiveFilters() ? _clearFilters : null,
                    icon: const Icon(Icons.clear_all, size: 18),
                    label: const Text('مسح الفلاتر', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      minimumSize: Size.zero,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر إنشاء جهاز جديد
                  ElevatedButton.icon(
                    onPressed: () => _showCreateDeviceDialog(context),
                    icon: const Icon(Icons.add, size: 18),
                    label: const Text('إضافة جهاز', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryGold,
                      foregroundColor: AppColors.darkBackground,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      minimumSize: Size.zero,
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),

              // فلاتر إضافية (صف ثاني)
              if (_hasAdvancedFilters()) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    // فلتر المستخدم
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedUser,
                        decoration: InputDecoration(
                          labelText: 'المستخدم',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('جميع المستخدمين')),
                          // سيتم تحميل المستخدمين ديناميكياً
                        ],
                        onChanged: (value) {
                          setState(() => _selectedUser = value);
                          ref.read(devicesProvider.notifier).filterByUser(value);
                        },
                      ),
                    ),

                    const SizedBox(width: 12),

                    // فلتر الموقع
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedLocation,
                        decoration: InputDecoration(
                          labelText: 'الموقع',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('جميع المواقع')),
                          // سيتم تحميل المواقع ديناميكياً
                        ],
                        onChanged: (value) {
                          setState(() => _selectedLocation = value);
                          ref.read(devicesProvider.notifier).filterByLocation(value);
                        },
                      ),
                    ),

                    const SizedBox(width: 12),

                    // زر إظهار/إخفاء الفلاتر المتقدمة
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          // تبديل حالة الفلاتر المتقدمة
                        });
                      },
                      icon: const Icon(Icons.expand_less),
                      label: const Text('إخفاء الفلاتر المتقدمة'),
                    ),
                  ],
                ),
              ] else ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton.icon(
                    onPressed: () {
                      setState(() {
                        // تبديل حالة الفلاتر المتقدمة
                      });
                    },
                    icon: const Icon(Icons.expand_more),
                    label: const Text('فلاتر متقدمة'),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  bool _hasActiveFilters() {
    return _searchController.text.isNotEmpty ||
        _selectedStatus != null ||
        _selectedType != null ||
        _selectedUser != null ||
        _selectedLocation != null;
  }

  bool _hasAdvancedFilters() {
    // يمكن تحديد متى تظهر الفلاتر المتقدمة
    return false; // مؤقتاً
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedStatus = null;
      _selectedType = null;
      _selectedUser = null;
      _selectedLocation = null;
    });
    
    ref.read(devicesProvider.notifier).clearFilters();
  }

  void _showCreateDeviceDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CreateDeviceDialog(),
    );
  }
}
