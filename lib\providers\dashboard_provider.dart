import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/supabase_service.dart';
import '../models/user_model.dart';
import '../models/location_model.dart';
import '../models/photo_model.dart';
import '../models/video_model.dart';

// Dashboard Statistics Model
class DashboardStats {
  final int totalUsers;
  final int totalLocations;
  final int totalPhotos;
  final int totalVideos;
  final int totalDevices;
  final int activeUsers;
  final int activeLocations;
  final double totalStorageUsed; // في MB
  final List<LocationModel> topLocations;
  final List<UserModel> recentUsers;
  final List<PhotoModel> recentPhotos;
  final List<VideoModel> recentVideos;

  const DashboardStats({
    required this.totalUsers,
    required this.totalLocations,
    required this.totalPhotos,
    required this.totalVideos,
    required this.totalDevices,
    required this.activeUsers,
    required this.activeLocations,
    required this.totalStorageUsed,
    required this.topLocations,
    required this.recentUsers,
    required this.recentPhotos,
    required this.recentVideos,
  });

  DashboardStats copyWith({
    int? totalUsers,
    int? totalLocations,
    int? totalPhotos,
    int? totalVideos,
    int? totalDevices,
    int? activeUsers,
    int? activeLocations,
    double? totalStorageUsed,
    List<LocationModel>? topLocations,
    List<UserModel>? recentUsers,
    List<PhotoModel>? recentPhotos,
    List<VideoModel>? recentVideos,
  }) {
    return DashboardStats(
      totalUsers: totalUsers ?? this.totalUsers,
      totalLocations: totalLocations ?? this.totalLocations,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      totalVideos: totalVideos ?? this.totalVideos,
      totalDevices: totalDevices ?? this.totalDevices,
      activeUsers: activeUsers ?? this.activeUsers,
      activeLocations: activeLocations ?? this.activeLocations,
      totalStorageUsed: totalStorageUsed ?? this.totalStorageUsed,
      topLocations: topLocations ?? this.topLocations,
      recentUsers: recentUsers ?? this.recentUsers,
      recentPhotos: recentPhotos ?? this.recentPhotos,
      recentVideos: recentVideos ?? this.recentVideos,
    );
  }
}

// Dashboard Provider
class DashboardNotifier extends StateNotifier<AsyncValue<DashboardStats>> {
  DashboardNotifier() : super(const AsyncValue.loading()) {
    loadDashboardData();
  }

  Future<void> loadDashboardData() async {
    try {
      state = const AsyncValue.loading();
      
      final supabase = SupabaseService.instance.client;
      
      // جلب الإحصائيات الأساسية
      final stats = await SupabaseService.instance.getQuickStats();
      
      // جلب المستخدمين النشطين
      final activeUsersResponse = await supabase
          .from('users')
          .select()
          .eq('is_active', true);
      
      final activeUsers = activeUsersResponse.length;
      
      // جلب المواقع النشطة
      final activeLocationsResponse = await supabase
          .from('locations')
          .select()
          .eq('is_active', true);
      
      final activeLocations = activeLocationsResponse.length;
      
      // جلب أكثر المواقع استخداماً
      final topLocationsResponse = await supabase
          .from('locations')
          .select()
          .order('total_photos', ascending: false)
          .order('total_videos', ascending: false)
          .limit(5);
      
      final topLocations = topLocationsResponse
          .map((json) => LocationModel.fromJson(json))
          .toList();
      
      // جلب المستخدمين الجدد
      final recentUsersResponse = await supabase
          .from('users')
          .select()
          .order('created_at', ascending: false)
          .limit(5);
      
      final recentUsers = recentUsersResponse
          .map((json) => UserModel.fromJson(json))
          .toList();
      
      // جلب الصور الحديثة
      final recentPhotosResponse = await supabase
          .from('photos')
          .select()
          .order('created_at', ascending: false)
          .limit(5);
      
      final recentPhotos = recentPhotosResponse
          .map((json) => PhotoModel.fromJson(json))
          .toList();
      
      // جلب الفيديوهات الحديثة
      final recentVideosResponse = await supabase
          .from('videos')
          .select()
          .order('created_at', ascending: false)
          .limit(5);
      
      final recentVideos = recentVideosResponse
          .map((json) => VideoModel.fromJson(json))
          .toList();
      
      // حساب التخزين المستخدم (تقديري)
      double totalStorageUsed = 0;
      for (final photo in recentPhotos) {
        if (photo.fileSizeBytes != null) {
          totalStorageUsed += photo.fileSizeBytes! / (1024 * 1024); // تحويل إلى MB
        }
      }
      
      final dashboardStats = DashboardStats(
        totalUsers: stats['users'] ?? 0,
        totalLocations: stats['locations'] ?? 0,
        totalPhotos: stats['photos'] ?? 0,
        totalVideos: stats['videos'] ?? 0,
        totalDevices: stats['devices'] ?? 0,
        activeUsers: activeUsers,
        activeLocations: activeLocations,
        totalStorageUsed: totalStorageUsed,
        topLocations: topLocations,
        recentUsers: recentUsers,
        recentPhotos: recentPhotos,
        recentVideos: recentVideos,
      );
      
      state = AsyncValue.data(dashboardStats);
      
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> refresh() async {
    await loadDashboardData();
  }
}

// Provider
final dashboardProvider = StateNotifierProvider<DashboardNotifier, AsyncValue<DashboardStats>>((ref) {
  return DashboardNotifier();
});

// Helper providers for individual stats
final totalUsersProvider = Provider<int>((ref) {
  final dashboard = ref.watch(dashboardProvider);
  return dashboard.when(
    data: (stats) => stats.totalUsers,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

final totalMediaProvider = Provider<int>((ref) {
  final dashboard = ref.watch(dashboardProvider);
  return dashboard.when(
    data: (stats) => stats.totalPhotos + stats.totalVideos,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

final activeLocationsProvider = Provider<int>((ref) {
  final dashboard = ref.watch(dashboardProvider);
  return dashboard.when(
    data: (stats) => stats.activeLocations,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

final storageUsedProvider = Provider<double>((ref) {
  final dashboard = ref.watch(dashboardProvider);
  return dashboard.when(
    data: (stats) => stats.totalStorageUsed,
    loading: () => 0.0,
    error: (_, __) => 0.0,
  );
});
