-- ===================================
-- إصلاح صلاحيات قاعدة البيانات
-- لحل مشكلة حذف وتعديل المستخدمين
-- ===================================

-- 1. إزالة جميع سياسات RLS الموجودة
DROP POLICY IF EXISTS "Enable read access for all users" ON users;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON users;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON users;
DROP POLICY IF EXISTS "Enable delete for authenticated users" ON users;
DROP POLICY IF EXISTS "Admin full access" ON users;
DROP POLICY IF EXISTS "Public read access" ON users;
DROP POLICY IF EXISTS "Admin only operations" ON users;

-- تكرار نفس الشيء للجداول الأخرى
DROP POLICY IF EXISTS "Enable read access for all users" ON devices;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON devices;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON devices;
DROP POLICY IF EXISTS "Enable delete for authenticated users" ON devices;

DROP POLICY IF EXISTS "Enable read access for all users" ON photos;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON photos;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON photos;
DROP POLICY IF EXISTS "Enable delete for authenticated users" ON photos;

DROP POLICY IF EXISTS "Enable read access for all users" ON videos;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON videos;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON videos;
DROP POLICY IF EXISTS "Enable delete for authenticated users" ON videos;

DROP POLICY IF EXISTS "Enable read access for all users" ON locations;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON locations;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON locations;
DROP POLICY IF EXISTS "Enable delete for authenticated users" ON locations;

-- 2. إلغاء تفعيل Row Level Security لجميع الجداول
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE devices DISABLE ROW LEVEL SECURITY;
ALTER TABLE photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE locations DISABLE ROW LEVEL SECURITY;

-- 3. منح صلاحيات كاملة للمستخدم المصادق عليه
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- 4. منح صلاحيات القراءة للمستخدم المجهول
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- 5. منح صلاحيات خاصة لـ service_role
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- 6. التأكد من صلاحيات الحذف والتحديث
GRANT DELETE ON users TO authenticated;
GRANT DELETE ON users TO service_role;
GRANT UPDATE ON users TO authenticated;
GRANT UPDATE ON users TO service_role;

GRANT DELETE ON devices TO authenticated;
GRANT DELETE ON devices TO service_role;
GRANT UPDATE ON devices TO authenticated;
GRANT UPDATE ON devices TO service_role;

-- 7. فحص حالة الجداول
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE 
        WHEN rowsecurity THEN '❌ مفعل'
        ELSE '✅ معطل'
    END as rls_status
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- 8. فحص الصلاحيات
SELECT 
    grantee,
    table_name,
    privilege_type
FROM information_schema.table_privileges 
WHERE table_schema = 'public' 
AND grantee IN ('authenticated', 'service_role', 'anon')
ORDER BY table_name, grantee, privilege_type;

-- 9. رسالة تأكيد
SELECT 'تم تطبيق جميع الصلاحيات بنجاح! 🎉' as status;

-- ===================================
-- ملاحظات مهمة:
-- ===================================
-- 1. هذا السكريپت يزيل جميع قيود الأمان
-- 2. مناسب للتطبيقات الشخصية فقط
-- 3. لا تستخدمه في بيئة الإنتاج مع مستخدمين متعددين
-- 4. تأكد من نسخ احتياطي قبل التطبيق
-- ===================================
