import 'package:latlong2/latlong.dart';

class ActiveUserModel {
  final String id;
  final String name;
  final String email;
  final String? avatar;
  final LatLng location;
  final String address;
  final String country;
  final String city;
  final DateTime lastSeen;
  final bool isOnline;
  final String deviceType;
  final String deviceModel;
  final int batteryLevel;
  final UserStatus status;
  final PrivacyLevel privacyLevel;
  final UserStats stats;
  final List<String> permissions;

  const ActiveUserModel({
    required this.id,
    required this.name,
    required this.email,
    this.avatar,
    required this.location,
    required this.address,
    required this.country,
    required this.city,
    required this.lastSeen,
    required this.isOnline,
    required this.deviceType,
    required this.deviceModel,
    required this.batteryLevel,
    required this.status,
    required this.privacyLevel,
    required this.stats,
    this.permissions = const [],
  });

  // حالة المستخدم
  bool get isActive => isOnline && DateTime.now().difference(lastSeen).inMinutes < 5;
  bool get isIdle => isOnline && DateTime.now().difference(lastSeen).inMinutes >= 5 && DateTime.now().difference(lastSeen).inMinutes < 15;
  bool get isAway => !isOnline || DateTime.now().difference(lastSeen).inMinutes >= 15;

  // لون المستخدم حسب الحالة
  String get statusColor {
    if (isActive) return '#4CAF50'; // أخضر
    if (isIdle) return '#FF9800'; // برتقالي
    return '#F44336'; // أحمر
  }

  // أيقونة المستخدم
  String get statusIcon {
    switch (status) {
      case UserStatus.working:
        return '💼';
      case UserStatus.photography:
        return '📸';
      case UserStatus.break_:
        return '☕';
      case UserStatus.meeting:
        return '🤝';
      case UserStatus.traveling:
        return '✈️';
      default:
        return '👤';
    }
  }

  // وصف الحالة
  String get statusDescription {
    switch (status) {
      case UserStatus.working:
        return 'يعمل';
      case UserStatus.photography:
        return 'يصور';
      case UserStatus.break_:
        return 'استراحة';
      case UserStatus.meeting:
        return 'في اجتماع';
      case UserStatus.traveling:
        return 'يسافر';
      default:
        return 'متاح';
    }
  }

  // وقت آخر ظهور
  String get lastSeenText {
    final diff = DateTime.now().difference(lastSeen);
    if (diff.inMinutes < 1) return 'الآن';
    if (diff.inMinutes < 60) return 'منذ ${diff.inMinutes} دقيقة';
    if (diff.inHours < 24) return 'منذ ${diff.inHours} ساعة';
    return 'منذ ${diff.inDays} يوم';
  }

  factory ActiveUserModel.fromJson(Map<String, dynamic> json) {
    return ActiveUserModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      avatar: json['avatar'] as String?,
      location: LatLng(
        json['latitude'] as double,
        json['longitude'] as double,
      ),
      address: json['address'] as String,
      country: json['country'] as String,
      city: json['city'] as String,
      lastSeen: DateTime.parse(json['last_seen'] as String),
      isOnline: json['is_online'] as bool,
      deviceType: json['device_type'] as String,
      deviceModel: json['device_model'] as String,
      batteryLevel: json['battery_level'] as int,
      status: UserStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => UserStatus.available,
      ),
      privacyLevel: PrivacyLevel.values.firstWhere(
        (e) => e.toString().split('.').last == json['privacy_level'],
        orElse: () => PrivacyLevel.public,
      ),
      stats: UserStats.fromJson(json['stats'] as Map<String, dynamic>),
      permissions: List<String>.from(json['permissions'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'latitude': location.latitude,
      'longitude': location.longitude,
      'address': address,
      'country': country,
      'city': city,
      'last_seen': lastSeen.toIso8601String(),
      'is_online': isOnline,
      'device_type': deviceType,
      'device_model': deviceModel,
      'battery_level': batteryLevel,
      'status': status.toString().split('.').last,
      'privacy_level': privacyLevel.toString().split('.').last,
      'stats': stats.toJson(),
      'permissions': permissions,
    };
  }

  ActiveUserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? avatar,
    LatLng? location,
    String? address,
    String? country,
    String? city,
    DateTime? lastSeen,
    bool? isOnline,
    String? deviceType,
    String? deviceModel,
    int? batteryLevel,
    UserStatus? status,
    PrivacyLevel? privacyLevel,
    UserStats? stats,
    List<String>? permissions,
  }) {
    return ActiveUserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      location: location ?? this.location,
      address: address ?? this.address,
      country: country ?? this.country,
      city: city ?? this.city,
      lastSeen: lastSeen ?? this.lastSeen,
      isOnline: isOnline ?? this.isOnline,
      deviceType: deviceType ?? this.deviceType,
      deviceModel: deviceModel ?? this.deviceModel,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      status: status ?? this.status,
      privacyLevel: privacyLevel ?? this.privacyLevel,
      stats: stats ?? this.stats,
      permissions: permissions ?? this.permissions,
    );
  }
}

// حالات المستخدم
enum UserStatus {
  available,
  working,
  photography,
  break_,
  meeting,
  traveling,
  offline,
}

// مستويات الخصوصية
enum PrivacyLevel {
  public,    // الجميع يراك
  friends,   // الأصدقاء فقط
  work,      // زملاء العمل فقط
  private,   // لا أحد يراك
}

// إحصائيات المستخدم
class UserStats {
  final int photosToday;
  final int videosToday;
  final int totalPhotos;
  final int totalVideos;
  final double hoursActiveToday;
  final double totalHoursActive;
  final int sessionsToday;
  final int totalSessions;
  final DateTime joinDate;

  const UserStats({
    required this.photosToday,
    required this.videosToday,
    required this.totalPhotos,
    required this.totalVideos,
    required this.hoursActiveToday,
    required this.totalHoursActive,
    required this.sessionsToday,
    required this.totalSessions,
    required this.joinDate,
  });

  int get totalMediaToday => photosToday + videosToday;
  int get totalMedia => totalPhotos + totalVideos;

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      photosToday: json['photos_today'] as int? ?? 0,
      videosToday: json['videos_today'] as int? ?? 0,
      totalPhotos: json['total_photos'] as int? ?? 0,
      totalVideos: json['total_videos'] as int? ?? 0,
      hoursActiveToday: (json['hours_active_today'] as num?)?.toDouble() ?? 0.0,
      totalHoursActive: (json['total_hours_active'] as num?)?.toDouble() ?? 0.0,
      sessionsToday: json['sessions_today'] as int? ?? 0,
      totalSessions: json['total_sessions'] as int? ?? 0,
      joinDate: DateTime.parse(json['join_date'] as String? ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'photos_today': photosToday,
      'videos_today': videosToday,
      'total_photos': totalPhotos,
      'total_videos': totalVideos,
      'hours_active_today': hoursActiveToday,
      'total_hours_active': totalHoursActive,
      'sessions_today': sessionsToday,
      'total_sessions': totalSessions,
      'join_date': joinDate.toIso8601String(),
    };
  }
}

// إشعار المستخدم
class UserNotification {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? data;

  const UserNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.data,
  });

  factory UserNotification.fromJson(Map<String, dynamic> json) {
    return UserNotification(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: json['type'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['is_read'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'is_read': isRead,
      'data': data,
    };
  }
}
