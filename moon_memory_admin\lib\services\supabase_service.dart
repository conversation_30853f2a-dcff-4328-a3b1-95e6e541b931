import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/constants/app_constants.dart';
import 'package:flutter/foundation.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();
  
  SupabaseService._();
  
  SupabaseClient? _client;
  SupabaseClient get client => _client!;
  
  bool get isInitialized => _client != null;
  
  Future<void> initialize() async {
    if (_client != null) return;
    
    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
    );
    
    _client = Supabase.instance.client;
    debugPrint('✅ Supabase initialized successfully');
  }
  
  // استكشاف بنية قاعدة البيانات
  Future<void> exploreDatabase() async {
    try {
      debugPrint('\n🔍 استكشاف بنية قاعدة البيانات...\n');

      // استكشاف جدول المستخدمين
      await _exploreTable('users');

      // استكشاف جدول الأجهزة
      await _exploreTable('devices');

      // استكشاف جدول المواقع
      await _exploreTable('locations');

      // استكشاف جدول الصور
      await _exploreTable('photos');

      // استكشاف جدول الفيديوهات
      await _exploreTable('videos');

    } catch (e) {
      debugPrint('❌ خطأ في استكشاف قاعدة البيانات: $e');
    }
  }

  // فحص تفصيلي لجدول الأجهزة
  Future<void> checkDevicesTable() async {
    try {
      debugPrint('\n🔍 فحص تفصيلي لجدول الأجهزة...\n');

      // جلب جميع الأجهزة
      final allDevices = await _client!.from('devices').select();
      debugPrint('📱 إجمالي الأجهزة في قاعدة البيانات: ${allDevices.length}');

      if (allDevices.isNotEmpty) {
        debugPrint('🔹 الأجهزة الموجودة:');
        for (var device in allDevices) {
          debugPrint('  • ID: ${device['id']}');
          debugPrint('    User ID: ${device['user_id']}');
          debugPrint('    Device Name: ${device['device_name'] ?? 'غير محدد'}');
          debugPrint('    Device Type: ${device['device_type'] ?? 'غير محدد'}');
          debugPrint('    Is Active: ${device['is_active'] ?? 'غير محدد'}');
          debugPrint('    Last Active: ${device['last_active_at'] ?? 'غير محدد'}');
          debugPrint('    Created At: ${device['created_at'] ?? 'غير محدد'}');
          debugPrint('');
        }
      } else {
        debugPrint('⚠️  لا توجد أجهزة مسجلة في قاعدة البيانات');
        debugPrint('💡 هذا يعني أن تطبيق الكاميرا لم يسجل أي جهاز بعد');
      }

      // فحص المستخدمين النشطين
      final activeUsers = await _client!
          .from('users')
          .select()
          .eq('is_active', true);

      debugPrint('👥 المستخدمين النشطين: ${activeUsers.length}');

      // فحص آخر نشاط في الصور والفيديوهات
      final recentPhotos = await _client!
          .from('photos')
          .select('user_id, username, upload_timestamp')
          .order('upload_timestamp', ascending: false)
          .limit(5);

      if (recentPhotos.isNotEmpty) {
        debugPrint('📸 آخر الصور المرفوعة:');
        for (var photo in recentPhotos) {
          debugPrint('  • ${photo['username']} - ${photo['upload_timestamp']}');
        }
      }

    } catch (e) {
      debugPrint('❌ خطأ في فحص جدول الأجهزة: $e');
    }
  }
  
  Future<void> _exploreTable(String tableName) async {
    try {
      debugPrint('📋 جدول: $tableName');
      debugPrint('=' * 50);
      
      // جلب عينة من البيانات لفهم البنية
      final response = await _client!
          .from(tableName)
          .select()
          .limit(1);

      if (response.isNotEmpty) {
        final sample = response.first;
        debugPrint('🔹 الأعمدة الموجودة:');

        sample.forEach((key, value) {
          final type = value.runtimeType.toString();
          final valueStr = value?.toString() ?? 'null';
          final displayValue = valueStr.length > 50
              ? '${valueStr.substring(0, 50)}...'
              : valueStr;

          debugPrint('  • $key: $type = $displayValue');
        });

        // عدد السجلات
        final countResponse = await _client!
            .from(tableName)
            .select('*')
            .count(CountOption.exact);

        debugPrint('📊 عدد السجلات: ${countResponse.count}');

      } else {
        debugPrint('⚠️  الجدول فارغ');
      }
      
      debugPrint('\n');
      
    } catch (e) {
      debugPrint('❌ خطأ في استكشاف جدول $tableName: $e\n');
    }
  }
  
  // جلب إحصائيات سريعة
  Future<Map<String, int>> getQuickStats() async {
    final stats = <String, int>{};
    
    try {
      // عدد المستخدمين
      final usersResponse = await _client!
          .from('users')
          .select('*')
          .count(CountOption.exact);
      stats['users'] = usersResponse.count;

      // عدد الأجهزة
      final devicesResponse = await _client!
          .from('devices')
          .select('*')
          .count(CountOption.exact);
      stats['devices'] = devicesResponse.count;

      // عدد المواقع
      final locationsResponse = await _client!
          .from('locations')
          .select('*')
          .count(CountOption.exact);
      stats['locations'] = locationsResponse.count;

      // عدد الصور
      final photosResponse = await _client!
          .from('photos')
          .select('*')
          .count(CountOption.exact);
      stats['photos'] = photosResponse.count;

      // عدد الفيديوهات
      final videosResponse = await _client!
          .from('videos')
          .select('*')
          .count(CountOption.exact);
      stats['videos'] = videosResponse.count;
      
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإحصائيات: $e');
    }
    
    return stats;
  }
}
