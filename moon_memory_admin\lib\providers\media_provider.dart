import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/photo_model.dart';
import '../models/video_model.dart';
import '../services/media_service.dart';

// حالة الوسائط
class MediaState {
  final List<PhotoModel> photos;
  final List<VideoModel> videos;
  final bool isLoading;
  final String? error;
  final int totalPhotos;
  final int totalVideos;
  final int currentPage;
  final int totalPages;
  final bool hasMore;
  final String? searchQuery;
  final String? userFilter;
  final String? deviceFilter;
  final String? locationFilter;
  final DateTime? startDateFilter;
  final DateTime? endDateFilter;
  final String mediaType; // 'all', 'photos', 'videos'

  const MediaState({
    this.photos = const [],
    this.videos = const [],
    this.isLoading = false,
    this.error,
    this.totalPhotos = 0,
    this.totalVideos = 0,
    this.currentPage = 1,
    this.totalPages = 0,
    this.hasMore = false,
    this.searchQuery,
    this.userFilter,
    this.deviceFilter,
    this.locationFilter,
    this.startDateFilter,
    this.endDateFilter,
    this.mediaType = 'all',
  });

  MediaState copyWith({
    List<PhotoModel>? photos,
    List<VideoModel>? videos,
    bool? isLoading,
    String? error,
    int? totalPhotos,
    int? totalVideos,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
    String? searchQuery,
    String? userFilter,
    String? deviceFilter,
    String? locationFilter,
    DateTime? startDateFilter,
    DateTime? endDateFilter,
    String? mediaType,
  }) {
    return MediaState(
      photos: photos ?? this.photos,
      videos: videos ?? this.videos,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      totalVideos: totalVideos ?? this.totalVideos,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
      searchQuery: searchQuery ?? this.searchQuery,
      userFilter: userFilter ?? this.userFilter,
      deviceFilter: deviceFilter ?? this.deviceFilter,
      locationFilter: locationFilter ?? this.locationFilter,
      startDateFilter: startDateFilter ?? this.startDateFilter,
      endDateFilter: endDateFilter ?? this.endDateFilter,
      mediaType: mediaType ?? this.mediaType,
    );
  }

  int get totalMedia => totalPhotos + totalVideos;
}

// مزود الوسائط
class MediaNotifier extends StateNotifier<MediaState> {
  MediaNotifier() : super(const MediaState()) {
    loadMedia();
  }

  final MediaService _mediaService = MediaService.instance;

  /// تحميل الوسائط
  Future<void> loadMedia({
    int page = 1,
    bool reset = false,
  }) async {
    if (reset) {
      state = state.copyWith(
        photos: [],
        videos: [],
        currentPage: 1,
        error: null,
      );
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      if (state.mediaType == 'all' || state.mediaType == 'photos') {
        await _loadPhotos(page, reset);
      }
      
      if (state.mediaType == 'all' || state.mediaType == 'videos') {
        await _loadVideos(page, reset);
      }

      state = state.copyWith(isLoading: false);
    } catch (error) {
      state = state.copyWith(
        error: 'خطأ في تحميل الوسائط: ${error.toString()}',
        isLoading: false,
      );
    }
  }

  Future<void> _loadPhotos(int page, bool reset) async {
    final result = await _mediaService.getPhotos(
      page: page,
      search: state.searchQuery,
      userId: state.userFilter,
      deviceId: state.deviceFilter,
      locationId: state.locationFilter,
      startDate: state.startDateFilter,
      endDate: state.endDateFilter,
    );

    if (result['success']) {
      final newPhotos = result['photos'] as List<PhotoModel>;
      final photos = reset || page == 1 ? newPhotos : [...state.photos, ...newPhotos];

      state = state.copyWith(
        photos: photos,
        totalPhotos: result['totalCount'],
        currentPage: result['currentPage'],
        totalPages: result['totalPages'],
        hasMore: result['hasMore'],
      );
    } else {
      state = state.copyWith(error: result['error']);
    }
  }

  Future<void> _loadVideos(int page, bool reset) async {
    final result = await _mediaService.getVideos(
      page: page,
      search: state.searchQuery,
      userId: state.userFilter,
      deviceId: state.deviceFilter,
      locationId: state.locationFilter,
      startDate: state.startDateFilter,
      endDate: state.endDateFilter,
    );

    if (result['success']) {
      final newVideos = result['videos'] as List<VideoModel>;
      final videos = reset || page == 1 ? newVideos : [...state.videos, ...newVideos];

      state = state.copyWith(
        videos: videos,
        totalVideos: result['totalCount'],
        currentPage: result['currentPage'],
        totalPages: result['totalPages'],
        hasMore: result['hasMore'],
      );
    } else {
      state = state.copyWith(error: result['error']);
    }
  }

  /// البحث في الوسائط
  Future<void> searchMedia(String query) async {
    state = state.copyWith(searchQuery: query);
    await loadMedia(reset: true);
  }

  /// فلترة الوسائط حسب النوع
  Future<void> filterByMediaType(String type) async {
    state = state.copyWith(mediaType: type);
    await loadMedia(reset: true);
  }

  /// فلترة الوسائط حسب المستخدم
  Future<void> filterByUser(String? userId) async {
    state = state.copyWith(userFilter: userId);
    await loadMedia(reset: true);
  }

  /// فلترة الوسائط حسب الجهاز
  Future<void> filterByDevice(String? deviceId) async {
    state = state.copyWith(deviceFilter: deviceId);
    await loadMedia(reset: true);
  }

  /// فلترة الوسائط حسب الموقع
  Future<void> filterByLocation(String? locationId) async {
    state = state.copyWith(locationFilter: locationId);
    await loadMedia(reset: true);
  }

  /// فلترة الوسائط حسب التاريخ
  Future<void> filterByDateRange(DateTime? startDate, DateTime? endDate) async {
    state = state.copyWith(
      startDateFilter: startDate,
      endDateFilter: endDate,
    );
    await loadMedia(reset: true);
  }

  /// مسح الفلاتر
  Future<void> clearFilters() async {
    state = state.copyWith(
      searchQuery: null,
      userFilter: null,
      deviceFilter: null,
      locationFilter: null,
      startDateFilter: null,
      endDateFilter: null,
      mediaType: 'all',
    );
    await loadMedia(reset: true);
  }

  /// حذف صورة
  Future<void> deletePhoto(String photoId) async {
    try {
      final result = await _mediaService.deletePhoto(photoId: photoId);
      
      if (result['success']) {
        await loadMedia(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }
    } catch (error) {
      state = state.copyWith(error: 'فشل في حذف الصورة: ${error.toString()}');
      rethrow;
    }
  }

  /// حذف فيديو
  Future<void> deleteVideo(String videoId) async {
    try {
      final result = await _mediaService.deleteVideo(videoId: videoId);
      
      if (result['success']) {
        await loadMedia(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }
    } catch (error) {
      state = state.copyWith(error: 'فشل في حذف الفيديو: ${error.toString()}');
      rethrow;
    }
  }

  /// تحديث وصف الصورة
  Future<void> updatePhotoDescription(String photoId, String description) async {
    try {
      final result = await _mediaService.updatePhotoDescription(
        photoId: photoId,
        description: description,
      );
      
      if (result['success']) {
        await loadMedia(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }
    } catch (error) {
      state = state.copyWith(error: 'فشل في تحديث وصف الصورة: ${error.toString()}');
      rethrow;
    }
  }

  /// تحديث وصف الفيديو
  Future<void> updateVideoDescription(String videoId, String description) async {
    try {
      final result = await _mediaService.updateVideoDescription(
        videoId: videoId,
        description: description,
      );
      
      if (result['success']) {
        await loadMedia(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }
    } catch (error) {
      state = state.copyWith(error: 'فشل في تحديث وصف الفيديو: ${error.toString()}');
      rethrow;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// تحديث القائمة
  void refresh() {
    loadMedia(reset: true);
  }

  /// تحميل المزيد من الوسائط
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;
    await loadMedia(page: state.currentPage + 1);
  }
}

// مزودات الوسائط
final mediaProvider = StateNotifierProvider<MediaNotifier, MediaState>((ref) {
  return MediaNotifier();
});

// مزود إحصائيات الوسائط
final mediaStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  return await MediaService.instance.getMediaStats();
});

// مزودات الإحصائيات المحددة
final totalPhotosCountProvider = Provider<int>((ref) {
  return ref.watch(mediaProvider).totalPhotos;
});

final totalVideosCountProvider = Provider<int>((ref) {
  return ref.watch(mediaProvider).totalVideos;
});

final totalMediaCountProvider = Provider<int>((ref) {
  return ref.watch(mediaProvider).totalMedia;
});
