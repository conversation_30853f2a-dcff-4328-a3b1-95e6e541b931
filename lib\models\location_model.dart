class LocationModel {
  final String id;
  final String locationCode; // U101, C101, etc.
  final String locationType; // U or C
  final String locationNumber; // 101, 102, etc.
  final String? locationNameAr;
  final String? locationNameEn;
  final int sortOrder;
  final bool isActive;
  final int totalPhotos;
  final int totalVideos;
  final double? latitude;
  final double? longitude;
  final String? address;
  final DateTime? lastUsedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // معلومات إضافية للخريطة
  final int? devicesCount;
  final List<String>? deviceIds;

  const LocationModel({
    required this.id,
    required this.locationCode,
    required this.locationType,
    required this.locationNumber,
    this.locationNameAr,
    this.locationNameEn,
    required this.sortOrder,
    this.isActive = true,
    this.totalPhotos = 0,
    this.totalVideos = 0,
    this.latitude,
    this.longitude,
    this.address,
    this.lastUsedAt,
    this.createdAt,
    this.updatedAt,
    this.devicesCount,
    this.deviceIds,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      id: json['id'] as String,
      locationCode: json['location_code'] as String,
      locationType: json['location_type'] as String,
      locationNumber: json['location_number'] as String,
      locationNameAr: json['location_name_ar'] as String?,
      locationNameEn: json['location_name_en'] as String?,
      sortOrder: json['sort_order'] as int,
      isActive: json['is_active'] as bool? ?? true,
      totalPhotos: json['total_photos'] as int? ?? 0,
      totalVideos: json['total_videos'] as int? ?? 0,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      address: json['address'] as String?,
      lastUsedAt: json['last_used_at'] != null
          ? DateTime.parse(json['last_used_at'] as String)
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      devicesCount: json['devices_count'] as int?,
      deviceIds: json['device_ids'] != null
          ? List<String>.from(json['device_ids'] as List)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'location_code': locationCode,
      'location_type': locationType,
      'location_number': locationNumber,
      'location_name_ar': locationNameAr,
      'location_name_en': locationNameEn,
      'sort_order': sortOrder,
      'is_active': isActive,
      'total_photos': totalPhotos,
      'total_videos': totalVideos,
      'last_used_at': lastUsedAt?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Helper getters
  bool get isULocation => locationType == 'U';
  bool get isCLocation => locationType == 'C';

  String get locationTypeArabic => isULocation ? 'جامعة' : 'كلية';
  String get displayName => locationNameAr ?? locationCode;
  int get totalMedia => totalPhotos + totalVideos;

  LocationModel copyWith({
    String? id,
    String? locationCode,
    String? locationType,
    String? locationNumber,
    String? locationNameAr,
    String? locationNameEn,
    int? sortOrder,
    bool? isActive,
    int? totalPhotos,
    int? totalVideos,
    double? latitude,
    double? longitude,
    String? address,
    DateTime? lastUsedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? devicesCount,
    List<String>? deviceIds,
  }) {
    return LocationModel(
      id: id ?? this.id,
      locationCode: locationCode ?? this.locationCode,
      locationType: locationType ?? this.locationType,
      locationNumber: locationNumber ?? this.locationNumber,
      locationNameAr: locationNameAr ?? this.locationNameAr,
      locationNameEn: locationNameEn ?? this.locationNameEn,
      sortOrder: sortOrder ?? this.sortOrder,
      isActive: isActive ?? this.isActive,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      totalVideos: totalVideos ?? this.totalVideos,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      devicesCount: devicesCount ?? this.devicesCount,
      deviceIds: deviceIds ?? this.deviceIds,
    );
  }

  @override
  String toString() {
    return 'LocationModel(id: $id, locationCode: $locationCode, locationNameAr: $locationNameAr, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
