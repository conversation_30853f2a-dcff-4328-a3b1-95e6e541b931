// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Controls_Maps_1_H
#define WINRT_Windows_UI_Xaml_Controls_Maps_1_H
#include "winrt/impl/Windows.UI.Xaml.Controls.Maps.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Controls::Maps
{
    struct __declspec(empty_bases) ICustomMapTileDataSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICustomMapTileDataSource>
    {
        ICustomMapTileDataSource(std::nullptr_t = nullptr) noexcept {}
        ICustomMapTileDataSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICustomMapTileDataSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICustomMapTileDataSourceFactory>
    {
        ICustomMapTileDataSourceFactory(std::nullptr_t = nullptr) noexcept {}
        ICustomMapTileDataSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHttpMapTileDataSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMapTileDataSource>
    {
        IHttpMapTileDataSource(std::nullptr_t = nullptr) noexcept {}
        IHttpMapTileDataSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHttpMapTileDataSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMapTileDataSourceFactory>
    {
        IHttpMapTileDataSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpMapTileDataSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILocalMapTileDataSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalMapTileDataSource>
    {
        ILocalMapTileDataSource(std::nullptr_t = nullptr) noexcept {}
        ILocalMapTileDataSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILocalMapTileDataSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalMapTileDataSourceFactory>
    {
        ILocalMapTileDataSourceFactory(std::nullptr_t = nullptr) noexcept {}
        ILocalMapTileDataSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapActualCameraChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapActualCameraChangedEventArgs>
    {
        IMapActualCameraChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapActualCameraChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapActualCameraChangedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapActualCameraChangedEventArgs2>
    {
        IMapActualCameraChangedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IMapActualCameraChangedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapActualCameraChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapActualCameraChangingEventArgs>
    {
        IMapActualCameraChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapActualCameraChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapActualCameraChangingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapActualCameraChangingEventArgs2>
    {
        IMapActualCameraChangingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IMapActualCameraChangingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapBillboard :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapBillboard>
    {
        IMapBillboard(std::nullptr_t = nullptr) noexcept {}
        IMapBillboard(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapBillboardFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapBillboardFactory>
    {
        IMapBillboardFactory(std::nullptr_t = nullptr) noexcept {}
        IMapBillboardFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapBillboardStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapBillboardStatics>
    {
        IMapBillboardStatics(std::nullptr_t = nullptr) noexcept {}
        IMapBillboardStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapCamera :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapCamera>
    {
        IMapCamera(std::nullptr_t = nullptr) noexcept {}
        IMapCamera(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapCameraFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapCameraFactory>
    {
        IMapCameraFactory(std::nullptr_t = nullptr) noexcept {}
        IMapCameraFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapContextRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapContextRequestedEventArgs>
    {
        IMapContextRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapContextRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl>
    {
        IMapControl(std::nullptr_t = nullptr) noexcept {}
        IMapControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl2>
    {
        IMapControl2(std::nullptr_t = nullptr) noexcept {}
        IMapControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl3>
    {
        IMapControl3(std::nullptr_t = nullptr) noexcept {}
        IMapControl3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl4>
    {
        IMapControl4(std::nullptr_t = nullptr) noexcept {}
        IMapControl4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl5>
    {
        IMapControl5(std::nullptr_t = nullptr) noexcept {}
        IMapControl5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl6>
    {
        IMapControl6(std::nullptr_t = nullptr) noexcept {}
        IMapControl6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl7>
    {
        IMapControl7(std::nullptr_t = nullptr) noexcept {}
        IMapControl7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControl8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControl8>
    {
        IMapControl8(std::nullptr_t = nullptr) noexcept {}
        IMapControl8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlBusinessLandmarkClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlBusinessLandmarkClickEventArgs>
    {
        IMapControlBusinessLandmarkClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlBusinessLandmarkClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlBusinessLandmarkPointerEnteredEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlBusinessLandmarkPointerEnteredEventArgs>
    {
        IMapControlBusinessLandmarkPointerEnteredEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlBusinessLandmarkPointerEnteredEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlBusinessLandmarkPointerExitedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlBusinessLandmarkPointerExitedEventArgs>
    {
        IMapControlBusinessLandmarkPointerExitedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlBusinessLandmarkPointerExitedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlBusinessLandmarkRightTappedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlBusinessLandmarkRightTappedEventArgs>
    {
        IMapControlBusinessLandmarkRightTappedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlBusinessLandmarkRightTappedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlDataHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlDataHelper>
    {
        IMapControlDataHelper(std::nullptr_t = nullptr) noexcept {}
        IMapControlDataHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlDataHelper2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlDataHelper2>
    {
        IMapControlDataHelper2(std::nullptr_t = nullptr) noexcept {}
        IMapControlDataHelper2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlDataHelperFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlDataHelperFactory>
    {
        IMapControlDataHelperFactory(std::nullptr_t = nullptr) noexcept {}
        IMapControlDataHelperFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlDataHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlDataHelperStatics>
    {
        IMapControlDataHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IMapControlDataHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlStatics>
    {
        IMapControlStatics(std::nullptr_t = nullptr) noexcept {}
        IMapControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlStatics2>
    {
        IMapControlStatics2(std::nullptr_t = nullptr) noexcept {}
        IMapControlStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlStatics4>
    {
        IMapControlStatics4(std::nullptr_t = nullptr) noexcept {}
        IMapControlStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlStatics5>
    {
        IMapControlStatics5(std::nullptr_t = nullptr) noexcept {}
        IMapControlStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlStatics6>
    {
        IMapControlStatics6(std::nullptr_t = nullptr) noexcept {}
        IMapControlStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlStatics7>
    {
        IMapControlStatics7(std::nullptr_t = nullptr) noexcept {}
        IMapControlStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlStatics8>
    {
        IMapControlStatics8(std::nullptr_t = nullptr) noexcept {}
        IMapControlStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlTransitFeatureClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlTransitFeatureClickEventArgs>
    {
        IMapControlTransitFeatureClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlTransitFeatureClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlTransitFeaturePointerEnteredEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlTransitFeaturePointerEnteredEventArgs>
    {
        IMapControlTransitFeaturePointerEnteredEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlTransitFeaturePointerEnteredEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlTransitFeaturePointerExitedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlTransitFeaturePointerExitedEventArgs>
    {
        IMapControlTransitFeaturePointerExitedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlTransitFeaturePointerExitedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlTransitFeatureRightTappedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlTransitFeatureRightTappedEventArgs>
    {
        IMapControlTransitFeatureRightTappedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapControlTransitFeatureRightTappedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapCustomExperience :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapCustomExperience>
    {
        IMapCustomExperience(std::nullptr_t = nullptr) noexcept {}
        IMapCustomExperience(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapCustomExperienceChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapCustomExperienceChangedEventArgs>
    {
        IMapCustomExperienceChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapCustomExperienceChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapCustomExperienceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapCustomExperienceFactory>
    {
        IMapCustomExperienceFactory(std::nullptr_t = nullptr) noexcept {}
        IMapCustomExperienceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElement>
    {
        IMapElement(std::nullptr_t = nullptr) noexcept {}
        IMapElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElement2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElement2>
    {
        IMapElement2(std::nullptr_t = nullptr) noexcept {}
        IMapElement2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElement3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElement3>
    {
        IMapElement3(std::nullptr_t = nullptr) noexcept {}
        IMapElement3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElement3D :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElement3D>
    {
        IMapElement3D(std::nullptr_t = nullptr) noexcept {}
        IMapElement3D(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElement3DStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElement3DStatics>
    {
        IMapElement3DStatics(std::nullptr_t = nullptr) noexcept {}
        IMapElement3DStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElement4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElement4>
    {
        IMapElement4(std::nullptr_t = nullptr) noexcept {}
        IMapElement4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementClickEventArgs>
    {
        IMapElementClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapElementClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementFactory>
    {
        IMapElementFactory(std::nullptr_t = nullptr) noexcept {}
        IMapElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementPointerEnteredEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementPointerEnteredEventArgs>
    {
        IMapElementPointerEnteredEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapElementPointerEnteredEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementPointerExitedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementPointerExitedEventArgs>
    {
        IMapElementPointerExitedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapElementPointerExitedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementStatics>
    {
        IMapElementStatics(std::nullptr_t = nullptr) noexcept {}
        IMapElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementStatics2>
    {
        IMapElementStatics2(std::nullptr_t = nullptr) noexcept {}
        IMapElementStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementStatics3>
    {
        IMapElementStatics3(std::nullptr_t = nullptr) noexcept {}
        IMapElementStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementStatics4>
    {
        IMapElementStatics4(std::nullptr_t = nullptr) noexcept {}
        IMapElementStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementsLayer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementsLayer>
    {
        IMapElementsLayer(std::nullptr_t = nullptr) noexcept {}
        IMapElementsLayer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementsLayerClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementsLayerClickEventArgs>
    {
        IMapElementsLayerClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapElementsLayerClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementsLayerContextRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementsLayerContextRequestedEventArgs>
    {
        IMapElementsLayerContextRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapElementsLayerContextRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementsLayerPointerEnteredEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementsLayerPointerEnteredEventArgs>
    {
        IMapElementsLayerPointerEnteredEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapElementsLayerPointerEnteredEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementsLayerPointerExitedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementsLayerPointerExitedEventArgs>
    {
        IMapElementsLayerPointerExitedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapElementsLayerPointerExitedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapElementsLayerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapElementsLayerStatics>
    {
        IMapElementsLayerStatics(std::nullptr_t = nullptr) noexcept {}
        IMapElementsLayerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapIcon :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapIcon>
    {
        IMapIcon(std::nullptr_t = nullptr) noexcept {}
        IMapIcon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapIcon2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapIcon2>
    {
        IMapIcon2(std::nullptr_t = nullptr) noexcept {}
        IMapIcon2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapIconStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapIconStatics>
    {
        IMapIconStatics(std::nullptr_t = nullptr) noexcept {}
        IMapIconStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapIconStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapIconStatics2>
    {
        IMapIconStatics2(std::nullptr_t = nullptr) noexcept {}
        IMapIconStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapInputEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapInputEventArgs>
    {
        IMapInputEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapInputEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapItemsControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapItemsControl>
    {
        IMapItemsControl(std::nullptr_t = nullptr) noexcept {}
        IMapItemsControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapItemsControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapItemsControlStatics>
    {
        IMapItemsControlStatics(std::nullptr_t = nullptr) noexcept {}
        IMapItemsControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapLayer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapLayer>
    {
        IMapLayer(std::nullptr_t = nullptr) noexcept {}
        IMapLayer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapLayerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapLayerFactory>
    {
        IMapLayerFactory(std::nullptr_t = nullptr) noexcept {}
        IMapLayerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapLayerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapLayerStatics>
    {
        IMapLayerStatics(std::nullptr_t = nullptr) noexcept {}
        IMapLayerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapModel3D :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapModel3D>
    {
        IMapModel3D(std::nullptr_t = nullptr) noexcept {}
        IMapModel3D(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapModel3DFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapModel3DFactory>
    {
        IMapModel3DFactory(std::nullptr_t = nullptr) noexcept {}
        IMapModel3DFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapModel3DStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapModel3DStatics>
    {
        IMapModel3DStatics(std::nullptr_t = nullptr) noexcept {}
        IMapModel3DStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapPolygon :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapPolygon>
    {
        IMapPolygon(std::nullptr_t = nullptr) noexcept {}
        IMapPolygon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapPolygon2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapPolygon2>
    {
        IMapPolygon2(std::nullptr_t = nullptr) noexcept {}
        IMapPolygon2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapPolygonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapPolygonStatics>
    {
        IMapPolygonStatics(std::nullptr_t = nullptr) noexcept {}
        IMapPolygonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapPolyline :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapPolyline>
    {
        IMapPolyline(std::nullptr_t = nullptr) noexcept {}
        IMapPolyline(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapPolylineStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapPolylineStatics>
    {
        IMapPolylineStatics(std::nullptr_t = nullptr) noexcept {}
        IMapPolylineStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRightTappedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRightTappedEventArgs>
    {
        IMapRightTappedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapRightTappedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteView>
    {
        IMapRouteView(std::nullptr_t = nullptr) noexcept {}
        IMapRouteView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteViewFactory>
    {
        IMapRouteViewFactory(std::nullptr_t = nullptr) noexcept {}
        IMapRouteViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapScene :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapScene>
    {
        IMapScene(std::nullptr_t = nullptr) noexcept {}
        IMapScene(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapSceneStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapSceneStatics>
    {
        IMapSceneStatics(std::nullptr_t = nullptr) noexcept {}
        IMapSceneStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapStyleSheet :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapStyleSheet>
    {
        IMapStyleSheet(std::nullptr_t = nullptr) noexcept {}
        IMapStyleSheet(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapStyleSheetEntriesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapStyleSheetEntriesStatics>
    {
        IMapStyleSheetEntriesStatics(std::nullptr_t = nullptr) noexcept {}
        IMapStyleSheetEntriesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapStyleSheetEntryStatesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapStyleSheetEntryStatesStatics>
    {
        IMapStyleSheetEntryStatesStatics(std::nullptr_t = nullptr) noexcept {}
        IMapStyleSheetEntryStatesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapStyleSheetStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapStyleSheetStatics>
    {
        IMapStyleSheetStatics(std::nullptr_t = nullptr) noexcept {}
        IMapStyleSheetStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTargetCameraChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTargetCameraChangedEventArgs>
    {
        IMapTargetCameraChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapTargetCameraChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTargetCameraChangedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTargetCameraChangedEventArgs2>
    {
        IMapTargetCameraChangedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IMapTargetCameraChangedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileBitmapRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileBitmapRequest>
    {
        IMapTileBitmapRequest(std::nullptr_t = nullptr) noexcept {}
        IMapTileBitmapRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileBitmapRequestDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileBitmapRequestDeferral>
    {
        IMapTileBitmapRequestDeferral(std::nullptr_t = nullptr) noexcept {}
        IMapTileBitmapRequestDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileBitmapRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileBitmapRequestedEventArgs>
    {
        IMapTileBitmapRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapTileBitmapRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileBitmapRequestedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileBitmapRequestedEventArgs2>
    {
        IMapTileBitmapRequestedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IMapTileBitmapRequestedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileDataSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileDataSource>
    {
        IMapTileDataSource(std::nullptr_t = nullptr) noexcept {}
        IMapTileDataSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileDataSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileDataSourceFactory>
    {
        IMapTileDataSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IMapTileDataSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileSource>
    {
        IMapTileSource(std::nullptr_t = nullptr) noexcept {}
        IMapTileSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileSource2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileSource2>
    {
        IMapTileSource2(std::nullptr_t = nullptr) noexcept {}
        IMapTileSource2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileSourceFactory>
    {
        IMapTileSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IMapTileSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileSourceStatics>
    {
        IMapTileSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IMapTileSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileSourceStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileSourceStatics2>
    {
        IMapTileSourceStatics2(std::nullptr_t = nullptr) noexcept {}
        IMapTileSourceStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileUriRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileUriRequest>
    {
        IMapTileUriRequest(std::nullptr_t = nullptr) noexcept {}
        IMapTileUriRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileUriRequestDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileUriRequestDeferral>
    {
        IMapTileUriRequestDeferral(std::nullptr_t = nullptr) noexcept {}
        IMapTileUriRequestDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileUriRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileUriRequestedEventArgs>
    {
        IMapTileUriRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMapTileUriRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapTileUriRequestedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapTileUriRequestedEventArgs2>
    {
        IMapTileUriRequestedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IMapTileUriRequestedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStreetsideExperience :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreetsideExperience>
    {
        IStreetsideExperience(std::nullptr_t = nullptr) noexcept {}
        IStreetsideExperience(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStreetsideExperienceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreetsideExperienceFactory>
    {
        IStreetsideExperienceFactory(std::nullptr_t = nullptr) noexcept {}
        IStreetsideExperienceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStreetsidePanorama :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreetsidePanorama>
    {
        IStreetsidePanorama(std::nullptr_t = nullptr) noexcept {}
        IStreetsidePanorama(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStreetsidePanoramaStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreetsidePanoramaStatics>
    {
        IStreetsidePanoramaStatics(std::nullptr_t = nullptr) noexcept {}
        IStreetsidePanoramaStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
