import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/media_provider.dart';
import '../../models/photo_model.dart';
import '../../models/video_model.dart';

class MediaGridView extends ConsumerWidget {
  const MediaGridView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaState = ref.watch(mediaProvider);

    // دمج الصور والفيديوهات في قائمة واحدة
    final allMedia = <dynamic>[
      ...mediaState.photos,
      ...mediaState.videos,
    ];

    // ترتيب حسب تاريخ الإنشاء
    allMedia.sort((a, b) {
      final aDate = a is PhotoModel ? a.createdAt : (a as VideoModel).createdAt;
      final bDate = b is PhotoModel ? b.createdAt : (b as VideoModel).createdAt;
      
      if (aDate == null && bDate == null) return 0;
      if (aDate == null) return 1;
      if (bDate == null) return -1;
      
      return bDate.compareTo(aDate);
    });

    return Column(
      children: [
        // الشبكة
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              itemCount: allMedia.length,
              itemBuilder: (context, index) {
                final item = allMedia[index];
                return _buildMediaCard(context, ref, item);
              },
            ),
          ),
        ),

        // شريط التنقل بين الصفحات
        if (mediaState.totalPages > 1) _buildPaginationBar(context, ref, mediaState),
      ],
    );
  }

  Widget _buildMediaCard(BuildContext context, WidgetRef ref, dynamic item) {
    final isPhoto = item is PhotoModel;
    final fileName = isPhoto ? item.fileName : (item as VideoModel).fileName;
    final description = isPhoto ? item.description : item.description;
    final createdAt = isPhoto ? item.createdAt : item.createdAt;
    final fileSize = isPhoto ? item.fileSizeBytes : item.fileSizeBytes;
    final username = isPhoto ? item.username : item.username;

    return Card(
      elevation: 2,
      color: AppColors.cardBackground,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // معاينة الوسائط
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.surfaceBackground,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      isPhoto ? Icons.photo : Icons.videocam,
                      size: 48,
                      color: isPhoto ? AppColors.success : AppColors.info,
                    ),
                  ),
                  
                  // نوع الملف
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: isPhoto ? AppColors.success : AppColors.info,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        isPhoto ? 'صورة' : 'فيديو',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // حجم الملف
                  if (fileSize != null)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _formatFileSize(fileSize),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // معلومات الملف
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم الملف
                  Text(
                    fileName,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.primaryText,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // الوصف
                  if (description?.isNotEmpty == true)
                    Text(
                      description!,
                      style: TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                  const Spacer(),

                  // معلومات إضافية
                  Row(
                    children: [
                      // المستخدم
                      Icon(
                        Icons.person,
                        size: 12,
                        color: AppColors.secondaryText,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          username,
                          style: TextStyle(
                            color: AppColors.secondaryText,
                            fontSize: 10,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  // التاريخ
                  Row(
                    children: [
                      // التاريخ
                      if (createdAt != null) ...[
                        Icon(
                          Icons.access_time,
                          size: 12,
                          color: AppColors.secondaryText,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(createdAt),
                          style: TextStyle(
                            color: AppColors.secondaryText,
                            fontSize: 10,
                          ),
                        ),
                      ],

                      const Spacer(),

                      // حجم الملف
                      if (fileSize != null) ...[
                        Icon(
                          Icons.storage,
                          size: 12,
                          color: AppColors.secondaryText,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatFileSize(fileSize),
                          style: TextStyle(
                            color: AppColors.secondaryText,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),

          // أزرار الإجراءات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.surfaceBackground,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر العرض
                IconButton(
                  onPressed: () => _showMediaViewer(context, item),
                  icon: const Icon(Icons.visibility, size: 16),
                  color: AppColors.info,
                  tooltip: 'عرض',
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),

                // زر التعديل
                IconButton(
                  onPressed: () => _showEditDialog(context, ref, item),
                  icon: const Icon(Icons.edit, size: 16),
                  color: AppColors.primaryGold,
                  tooltip: 'تعديل',
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),

                // زر الحذف
                IconButton(
                  onPressed: () => _showDeleteConfirmation(context, ref, item),
                  icon: const Icon(Icons.delete, size: 16),
                  color: AppColors.error,
                  tooltip: 'حذف',
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaginationBar(BuildContext context, WidgetRef ref, MediaState mediaState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Text(
            'عرض ${mediaState.photos.length + mediaState.videos.length} من أصل ${mediaState.totalMedia} عنصر',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 14,
            ),
          ),
          const Spacer(),
          
          // أزرار التنقل
          Row(
            children: [
              IconButton(
                onPressed: mediaState.currentPage > 1
                    ? () => ref.read(mediaProvider.notifier).loadMedia(page: 1, reset: true)
                    : null,
                icon: const Icon(Icons.first_page),
                tooltip: 'الصفحة الأولى',
              ),
              IconButton(
                onPressed: mediaState.currentPage > 1
                    ? () => ref.read(mediaProvider.notifier).loadMedia(
                          page: mediaState.currentPage - 1,
                          reset: true,
                        )
                    : null,
                icon: const Icon(Icons.chevron_left),
                tooltip: 'الصفحة السابقة',
              ),
              
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.primaryGold.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${mediaState.currentPage} من ${mediaState.totalPages}',
                  style: const TextStyle(
                    color: AppColors.primaryGold,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              IconButton(
                onPressed: mediaState.currentPage < mediaState.totalPages
                    ? () => ref.read(mediaProvider.notifier).loadMedia(
                          page: mediaState.currentPage + 1,
                          reset: true,
                        )
                    : null,
                icon: const Icon(Icons.chevron_right),
                tooltip: 'الصفحة التالية',
              ),
              IconButton(
                onPressed: mediaState.currentPage < mediaState.totalPages
                    ? () => ref.read(mediaProvider.notifier).loadMedia(
                          page: mediaState.totalPages,
                          reset: true,
                        )
                    : null,
                icon: const Icon(Icons.last_page),
                tooltip: 'الصفحة الأخيرة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  void _showMediaViewer(BuildContext context, dynamic item) {
    final isPhoto = item is PhotoModel;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('عرض ${isPhoto ? 'الصورة' : 'الفيديو'}'),
        content: Text('سيتم تطوير عارض ${isPhoto ? 'الصور' : 'الفيديوهات'} قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showEditDialog(BuildContext context, WidgetRef ref, dynamic item) {
    final isPhoto = item is PhotoModel;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل ${isPhoto ? 'الصورة' : 'الفيديو'}'),
        content: Text('سيتم تطوير تعديل ${isPhoto ? 'الصور' : 'الفيديوهات'} قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, dynamic item) {
    final isPhoto = item is PhotoModel;
    final fileName = isPhoto ? item.fileName : (item as VideoModel).fileName;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف ${isPhoto ? 'الصورة' : 'الفيديو'}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف ${isPhoto ? 'الصورة' : 'الفيديو'} "$fileName"؟'),
            const SizedBox(height: 8),
            const Text(
              'تحذير: سيتم حذف الملف نهائياً ولا يمكن استرداده.',
              style: TextStyle(
                color: AppColors.error,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                if (isPhoto) {
                  await ref.read(mediaProvider.notifier).deletePhoto(item.id);
                } else {
                  await ref.read(mediaProvider.notifier).deleteVideo(item.id);
                }
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف ${isPhoto ? 'الصورة' : 'الفيديو'} بنجاح'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (error) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل في حذف ${isPhoto ? 'الصورة' : 'الفيديو'}: ${error.toString()}'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
