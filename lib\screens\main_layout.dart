import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_colors.dart';
import '../widgets/layout/sidebar_navigation.dart';
import '../widgets/layout/top_app_bar.dart';
import 'dashboard_screen.dart';
import 'users_screen.dart';
import 'locations_screen.dart';
// import 'media_gallery_screen.dart'; // unused import
import 'devices_screen.dart';
// import 'reports_screen.dart'; // unused import
// import 'settings_screen.dart'; // unused import
import 'database_explorer_screen.dart';

// Navigation State Providers
final currentPageProvider = StateProvider<int>((ref) => 0);
final sidebarCollapsedProvider = StateProvider<bool>((ref) => false);

class MainLayout extends ConsumerWidget {
  const MainLayout({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPage = ref.watch(currentPageProvider);

    return Scaffold(
      backgroundColor: AppColors.darkBackground,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Row(
          textDirection: TextDirection.rtl, // لجعل القائمة على اليمين
          children: [
            // القائمة الجانبية
            const SidebarNavigation(),

            // المحتوى الرئيسي
            Expanded(
              child: Column(
                children: [
                  // الشريط العلوي
                  const TopAppBar(),

                  // محتوى الصفحة
                  Expanded(
                    child: _buildPageContent(currentPage),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageContent(int pageIndex) {
    switch (pageIndex) {
      case 0:
        return const DashboardScreen();
      case 1:
        return const UsersScreen();
      case 2:
        return const LocationsScreen();
      case 3:
        return const MediaGalleryScreen();
      case 4:
        return const DevicesScreen();
      case 5:
        return const ReportsScreen();
      case 6:
        return const SettingsScreen();
      case 7:
        return const DatabaseExplorerScreen();
      default:
        return const DashboardScreen();
    }
  }
}

// Placeholder screens - سننشئها لاحقاً

class MediaGalleryScreen extends StatelessWidget {
  const MediaGalleryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholder('معرض الوسائط', Icons.photo_library);
  }
}

// تم نقل DevicesScreen إلى ملف منفصل

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholder('التقارير', Icons.analytics);
  }
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholder('الإعدادات', Icons.settings);
  }
}

Widget _buildPlaceholder(String title, IconData icon) {
  return Container(
    padding: const EdgeInsets.all(24),
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primaryGold.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 64,
              color: AppColors.primaryGold,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryGold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'هذا القسم قيد التطوير',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.secondaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة المحتوى في التحديثات القادمة',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.mutedText,
            ),
          ),
        ],
      ),
    ),
  );
}
