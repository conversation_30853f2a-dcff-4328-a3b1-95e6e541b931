import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:latlong2/latlong.dart';
import '../models/active_user_model.dart';
import 'user_profile_service.dart';
// import 'admin_supabase_service.dart'; // unused import
import 'supabase_service.dart';
import 'package:flutter/foundation.dart';

class ActiveUsersService {
  static final ActiveUsersService _instance = ActiveUsersService._internal();
  factory ActiveUsersService() => _instance;
  ActiveUsersService._internal();

  // Stream للمستخدمين النشطين
  final StreamController<List<ActiveUserModel>> _usersController = 
      StreamController<List<ActiveUserModel>>.broadcast();
  
  // Stream للإشعارات
  final StreamController<UserNotification> _notificationsController = 
      StreamController<UserNotification>.broadcast();

  Stream<List<ActiveUserModel>> get usersStream => _usersController.stream;
  Stream<UserNotification> get notificationsStream => _notificationsController.stream;

  List<ActiveUserModel> _activeUsers = [];
  Timer? _updateTimer;
  ActiveUserModel? _currentUser;

  // بدء الخدمة
  Future<void> startService() async {
    debugPrint('🚀 بدء خدمة تتبع المستخدمين...');
    await _initializeRealUser();
    _startPeriodicUpdates();
    debugPrint('✅ خدمة تتبع المستخدمين جاهزة');
  }

  // إيقاف الخدمة
  void stopService() {
    _updateTimer?.cancel();
    _usersController.close();
    _notificationsController.close();
    debugPrint('⏹️ خدمة تتبع المستخدمين توقفت');
  }

  // الحصول على المستخدم الحالي
  Future<ActiveUserModel?> getCurrentUser() async {
    if (_currentUser != null) return _currentUser;

    try {
      // الحصول على الموقع الحالي
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // الحصول على العنوان
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      final placemark = placemarks.first;

      // الحصول على بيانات المستخدم من قاعدة البيانات
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: LatLng(position.latitude, position.longitude),
        address: '${placemark.locality}, ${placemark.country}',
        country: placemark.country ?? 'غير محدد',
        city: placemark.locality ?? 'غير محدد',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser

      debugPrint('📍 تم إضافة موقعك الحقيقي: ${position.latitude}, ${position.longitude}');
      debugPrint('🏠 العنوان: ${_currentUser!.address}');

      return _currentUser;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المستخدم الحالي: $e');

      // إنشاء مستخدم افتراضي في صنعاء إذا فشل الحصول على الموقع
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: const LatLng(15.3694, 44.1910), // صنعاء، اليمن
        address: 'صنعاء، اليمن',
        country: 'اليمن',
        city: 'صنعاء',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser فقط

      debugPrint('📍 تم إضافة موقع افتراضي في صنعاء');

      return _currentUser;
    }
  }

  // تهيئة جميع المستخدمين من قاعدة البيانات
  Future<void> _initializeRealUser() async {
    debugPrint('🔄 بدء تهيئة المستخدمين...');
    _activeUsers.clear();

    try {
      // الحصول على المستخدم الحقيقي (المدير)
      final currentUser = await getCurrentUser();

      // جلب جميع المستخدمين من قاعدة البيانات
      final allUsers = await _loadUsersFromDatabase();

      if (currentUser != null) {
        // إضافة المستخدم الحقيقي أولاً
        _activeUsers.add(currentUser);
      }

      // إضافة باقي المستخدمين من قاعدة البيانات
      _activeUsers.addAll(allUsers);

      // إرسال التحديث فوراً
      _usersController.add(List.from(_activeUsers));

      // إرسال إشعار
      _sendNotification(
        'مرحباً بك',
        'تم تسجيل دخولك من ${currentUser?.city ?? 'موقع غير محدد'}. يوجد ${_activeUsers.length} مستخدم متصل',
        'user_login',
      );

      debugPrint('✅ تم تهيئة ${_activeUsers.length} مستخدم');
      if (currentUser != null) {
        debugPrint('👤 المدير: ${currentUser.name} من ${currentUser.city}');
      }
      debugPrint('👥 مستخدمون آخرون: ${allUsers.length}');
      debugPrint('📊 إجمالي المستخدمين النشطين: ${_activeUsers.length}');

    } catch (e) {
      debugPrint('❌ خطأ في تهيئة المستخدمين: $e');
      _usersController.add([]);
    }
  }

  // جلب المستخدمين من قاعدة البيانات مع البيانات الحقيقية
  Future<List<ActiveUserModel>> _loadUsersFromDatabase() async {
    try {
      debugPrint('🔍 🆕 جلب المستخدمين الحقيقيين من قاعدة البيانات (النسخة المحدثة)...');

      // استخدام الخدمة العادية بدلاً من الإدارية للحصول على البيانات الحقيقية
      final supabase = SupabaseService.instance.client;

      // جلب جميع المستخدمين مع جميع البيانات
      final response = await supabase
          .from('users')
          .select('*')
          .eq('is_active', true)
          .order('last_login', ascending: false);

      debugPrint('📊 تم جلب ${response.length} مستخدم حقيقي من قاعدة البيانات');

      List<ActiveUserModel> users = [];

      for (int i = 0; i < response.length; i++) {
        final userData = response[i];

        debugPrint('🔍 فحص المستخدم ${i + 1}:');
        debugPrint('   📧 البريد: ${userData['email']}');
        debugPrint('   👤 الاسم: ${userData['full_name']}');
        debugPrint('   👑 مدير: ${userData['is_admin']}');
        debugPrint('   ✅ نشط: ${userData['is_active']}');
        debugPrint('   🆔 ID: ${userData['id']}');

        // تخطي المستخدم الحالي (المدير) لأنه سيتم إضافته منفصلاً
        // البحث عن المدير بناءً على البريد الإلكتروني أو كونه أول مستخدم
        if (userData['email'] == _currentUser?.email ||
            userData['email'] == '<EMAIL>' ||
            userData['is_admin'] == true) {
          debugPrint('   ⏭️ تم تخطي هذا المستخدم (مدير أو مستخدم حالي)');
          continue;
        }

        debugPrint('   ✅ سيتم معالجة هذا المستخدم...');

        // جلب الموقع الحقيقي للمستخدم من جدول الأجهزة أو المواقع
        debugPrint('   📍 جلب الموقع...');
        final userLocation = await _getUserRealLocation(userData['id']);
        debugPrint('   📍 الموقع: ${userLocation['city']}, ${userLocation['country']}');

        // جلب إحصائيات المستخدم الحقيقية
        debugPrint('   📊 جلب الإحصائيات...');
        final userStats = await _getUserRealStats(userData['id']);
        debugPrint('   📊 الصور: ${userStats.totalPhotos}, الفيديوهات: ${userStats.totalVideos}');

        // جلب حالة الاتصال الحقيقية
        debugPrint('   📱 جلب معلومات الجهاز...');
        final deviceInfo = await _getUserDeviceInfo(userData['id']);
        debugPrint('   📱 متصل: ${deviceInfo['isOnline']}, الجهاز: ${deviceInfo['deviceType']}');

        final user = ActiveUserModel(
          id: userData['id'],
          name: userData['full_name'] ?? 'مستخدم غير محدد',
          email: userData['email'] ?? '',
          location: userLocation['location'],
          address: userLocation['address'],
          country: userLocation['country'],
          city: userLocation['city'],
          lastSeen: userData['last_login'] != null
              ? DateTime.parse(userData['last_login'])
              : DateTime.parse(userData['created_at']),
          isOnline: deviceInfo['isOnline'],
          deviceType: deviceInfo['deviceType'],
          deviceModel: deviceInfo['deviceModel'],
          batteryLevel: deviceInfo['batteryLevel'],
          status: _getUserStatusFromData(userData),
          privacyLevel: PrivacyLevel.public,
          stats: userStats,
        );

        users.add(user);
        debugPrint('   ✅ تم إضافة المستخدم: ${user.name}');
        debugPrint('   ─────────────────────────────────────────────────────────────');
      }

      debugPrint('✅ تم إنشاء ${users.length} مستخدم نشط بالبيانات الحقيقية');
      return users;

    } catch (e) {
      debugPrint('❌ خطأ في جلب المستخدمين من قاعدة البيانات: $e');
      return [];
    }
  }

  // جلب الموقع الحقيقي للمستخدم
  Future<Map<String, dynamic>> _getUserRealLocation(String userId) async {
    try {
      final supabase = SupabaseService.instance.client;

      // محاولة جلب آخر موقع من جدول الأجهزة
      final deviceLocation = await supabase
          .from('devices')
          .select('last_latitude, last_longitude, last_location_name')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('last_active_at', ascending: false)
          .limit(1)
          .maybeSingle();

      if (deviceLocation != null &&
          deviceLocation['last_latitude'] != null &&
          deviceLocation['last_longitude'] != null) {

        final lat = deviceLocation['last_latitude'] as double;
        final lng = deviceLocation['last_longitude'] as double;
        final locationName = deviceLocation['last_location_name'] as String?;

        // تحليل اسم الموقع لاستخراج المدينة والبلد
        final locationParts = locationName?.split(', ') ?? ['موقع غير محدد', 'غير محدد'];
        final city = locationParts.isNotEmpty ? locationParts[0] : 'غير محدد';
        final country = locationParts.length > 1 ? locationParts.last : 'غير محدد';

        return {
          'location': LatLng(lat, lng),
          'address': locationName ?? '$city, $country',
          'city': city,
          'country': country,
        };
      }

      // إذا لم يوجد موقع في الأجهزة، استخدم موقع افتراضي بناءً على البيانات المتاحة
      return _getDefaultLocationForUser(userId);

    } catch (e) {
      debugPrint('⚠️ خطأ في جلب موقع المستخدم $userId: $e');
      return _getDefaultLocationForUser(userId);
    }
  }

  // موقع افتراضي للمستخدم
  Map<String, dynamic> _getDefaultLocationForUser(String userId) {
    // استخدام hash من ID المستخدم لتوزيع عشوائي ولكن ثابت
    final hash = userId.hashCode.abs();
    final locations = [
      {'lat': 15.3694, 'lng': 44.1910, 'city': 'صنعاء', 'country': 'اليمن'},
      {'lat': 14.7937, 'lng': 42.9441, 'city': 'الحديدة', 'country': 'اليمن'},
      {'lat': 16.9402, 'lng': 43.7445, 'city': 'صعدة', 'country': 'اليمن'},
      {'lat': 13.5779, 'lng': 48.5160, 'city': 'عدن', 'country': 'اليمن'},
      {'lat': 14.5518, 'lng': 49.1233, 'city': 'المكلا', 'country': 'اليمن'},
    ];

    final location = locations[hash % locations.length];

    return {
      'location': LatLng(location['lat']! as double, location['lng']! as double),
      'address': '${location['city']}, ${location['country']}',
      'city': location['city']! as String,
      'country': location['country']! as String,
    };
  }

  // جلب معلومات الجهاز الحقيقية
  Future<Map<String, dynamic>> _getUserDeviceInfo(String userId) async {
    try {
      final supabase = SupabaseService.instance.client;

      // جلب آخر جهاز نشط للمستخدم
      final device = await supabase
          .from('devices')
          .select('device_name, device_type, is_active, last_active_at, battery_level')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('last_active_at', ascending: false)
          .limit(1)
          .maybeSingle();

      if (device != null) {
        final lastActive = device['last_active_at'] != null
            ? DateTime.parse(device['last_active_at'])
            : DateTime.now().subtract(const Duration(hours: 1));

        // المستخدم متصل إذا كان آخر نشاط خلال آخر 5 دقائق
        final isOnline = DateTime.now().difference(lastActive).inMinutes < 5;

        return {
          'isOnline': isOnline,
          'deviceType': device['device_type'] ?? 'Unknown',
          'deviceModel': device['device_name'] ?? 'Unknown Device',
          'batteryLevel': device['battery_level'] ?? 100,
          'lastActive': lastActive,
        };
      }

      // إذا لم يوجد جهاز، إرجاع قيم افتراضية
      return {
        'isOnline': false,
        'deviceType': 'Unknown',
        'deviceModel': 'No Device',
        'batteryLevel': 0,
        'lastActive': DateTime.now().subtract(const Duration(days: 1)),
      };

    } catch (e) {
      debugPrint('⚠️ خطأ في جلب معلومات جهاز المستخدم $userId: $e');
      return {
        'isOnline': false,
        'deviceType': 'Unknown',
        'deviceModel': 'Unknown',
        'batteryLevel': 0,
        'lastActive': DateTime.now().subtract(const Duration(days: 1)),
      };
    }
  }

  // جلب إحصائيات المستخدم الحقيقية
  Future<UserStats> _getUserRealStats(String userId) async {
    try {
      final supabase = SupabaseService.instance.client;

      // جلب إحصائيات الصور
      final photosToday = await supabase
          .from('photos')
          .select('id')
          .eq('user_id', userId)
          .gte('upload_timestamp', DateTime.now().toIso8601String().substring(0, 10))
          .count();

      final totalPhotos = await supabase
          .from('photos')
          .select('id')
          .eq('user_id', userId)
          .count();

      // جلب إحصائيات الفيديوهات
      final videosToday = await supabase
          .from('videos')
          .select('id')
          .eq('user_id', userId)
          .gte('upload_timestamp', DateTime.now().toIso8601String().substring(0, 10))
          .count();

      final totalVideos = await supabase
          .from('videos')
          .select('id')
          .eq('user_id', userId)
          .count();

      // جلب معلومات المستخدم للحصول على تاريخ الانضمام
      final userInfo = await supabase
          .from('users')
          .select('created_at')
          .eq('id', userId)
          .single();

      return UserStats(
        photosToday: photosToday.count ?? 0,
        videosToday: videosToday.count ?? 0,
        totalPhotos: totalPhotos.count ?? 0,
        totalVideos: totalVideos.count ?? 0,
        hoursActiveToday: 0.0, // يمكن حسابها من جدول الأنشطة إذا وجد
        totalHoursActive: 0.0, // يمكن حسابها من جدول الأنشطة إذا وجد
        sessionsToday: 1, // يمكن حسابها من جدول الجلسات إذا وجد
        totalSessions: 1, // يمكن حسابها من جدول الجلسات إذا وجد
        joinDate: DateTime.parse(userInfo['created_at']),
      );

    } catch (e) {
      debugPrint('⚠️ خطأ في جلب إحصائيات المستخدم $userId: $e');
      // إرجاع إحصائيات افتراضية في حالة الخطأ
      return UserStats(
        photosToday: 0,
        videosToday: 0,
        totalPhotos: 0,
        totalVideos: 0,
        hoursActiveToday: 0.0,
        totalHoursActive: 0.0,
        sessionsToday: 0,
        totalSessions: 0,
        joinDate: DateTime.now().subtract(const Duration(days: 30)),
      );
    }
  }

  // تحديد حالة المستخدم من البيانات
  UserStatus _getUserStatusFromData(Map<String, dynamic> userData) {
    final lastLogin = userData['last_login'];

    if (lastLogin == null) {
      return UserStatus.offline;
    }

    final lastLoginDate = DateTime.parse(lastLogin);
    final now = DateTime.now();
    final difference = now.difference(lastLoginDate);

    if (difference.inMinutes < 5) {
      return UserStatus.working;
    } else if (difference.inMinutes < 15) {
      return UserStatus.available;
    } else if (difference.inHours < 24) {
      return UserStatus.break_;
    } else {
      return UserStatus.offline;
    }
  }



  // تحديثات دورية للمستخدم الحقيقي فقط
  void _startPeriodicUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateRealUserStatus();
    });
  }

  // تحديث حالة المستخدم الحقيقي
  void _updateRealUserStatus() {
    if (_currentUser != null && _activeUsers.isNotEmpty) {
      // تحديث آخر ظهور للمستخدم الحقيقي
      final updatedUser = _currentUser!.copyWith(
        lastSeen: DateTime.now(),
        isOnline: true,
        batteryLevel: 100, // افتراض أن الكمبيوتر متصل بالكهرباء
      );

      _currentUser = updatedUser;
      _activeUsers[0] = updatedUser;
      _usersController.add(_activeUsers);

      debugPrint('🔄 تم تحديث حالة المستخدم الحقيقي');
    }
  }



  // إرسال إشعار
  void _sendNotification(String title, String message, String type, {Map<String, dynamic>? data}) {
    final notification = UserNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );
    
    _notificationsController.add(notification);
  }

  // الحصول على المستخدمين النشطين
  List<ActiveUserModel> get activeUsers => List.unmodifiable(_activeUsers);

  // الحصول على المستخدمين حسب البلد
  Map<String, List<ActiveUserModel>> getUsersByCountry() {
    final Map<String, List<ActiveUserModel>> result = {};
    
    for (final user in _activeUsers) {
      if (!result.containsKey(user.country)) {
        result[user.country] = [];
      }
      result[user.country]!.add(user);
    }
    
    return result;
  }

  // الحصول على إحصائيات عامة
  Map<String, dynamic> getGlobalStats() {
    final totalUsers = _activeUsers.length;
    final onlineUsers = _activeUsers.where((u) => u.isOnline).length;
    final activeUsers = _activeUsers.where((u) => u.isActive).length;
    final totalPhotosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.photosToday);
    final totalVideosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.videosToday);
    
    return {
      'totalUsers': totalUsers,
      'onlineUsers': onlineUsers,
      'activeUsers': activeUsers,
      'offlineUsers': totalUsers - onlineUsers,
      'totalPhotosToday': totalPhotosToday,
      'totalVideosToday': totalVideosToday,
      'totalMediaToday': totalPhotosToday + totalVideosToday,
      'countries': getUsersByCountry().length,
    };
  }
}
