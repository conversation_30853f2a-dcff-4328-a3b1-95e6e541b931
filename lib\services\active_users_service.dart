import 'dart:async';
// import 'package:geolocator/geolocator.dart'; // مؤقتاً معطل
// import 'package:geocoding/geocoding.dart'; // مؤقتاً معطل
import 'package:latlong2/latlong.dart';
import '../models/active_user_model.dart';
import 'user_profile_service.dart';
// import 'admin_supabase_service.dart'; // unused import
import 'supabase_service.dart';
import 'package:flutter/foundation.dart';

class ActiveUsersService {
  static final ActiveUsersService _instance = ActiveUsersService._internal();
  factory ActiveUsersService() => _instance;
  ActiveUsersService._internal();

  // Stream للمستخدمين النشطين
  final StreamController<List<ActiveUserModel>> _usersController = 
      StreamController<List<ActiveUserModel>>.broadcast();
  
  // Stream للإشعارات
  final StreamController<UserNotification> _notificationsController = 
      StreamController<UserNotification>.broadcast();

  Stream<List<ActiveUserModel>> get usersStream => _usersController.stream;
  Stream<UserNotification> get notificationsStream => _notificationsController.stream;

  final List<ActiveUserModel> _activeUsers = [];
  Timer? _updateTimer;
  ActiveUserModel? _currentUser;

  // بدء الخدمة
  Future<void> startService() async {
    debugPrint('🚀 بدء خدمة تتبع المستخدمين...');
    await _initializeRealUser();
    _startPeriodicUpdates();
    debugPrint('✅ خدمة تتبع المستخدمين جاهزة');
  }

  // إيقاف الخدمة
  void stopService() {
    _updateTimer?.cancel();
    _usersController.close();
    _notificationsController.close();
    debugPrint('⏹️ خدمة تتبع المستخدمين توقفت');
  }

  // الحصول على المستخدم الحالي
  Future<ActiveUserModel?> getCurrentUser() async {
    if (_currentUser != null) return _currentUser;

    try {
      // مؤقتاً: استخدام موقع افتراضي بدلاً من GPS
      // Position position = await Geolocator.getCurrentPosition(
      //   desiredAccuracy: LocationAccuracy.high,
      // );

      // الحصول على العنوان
      // List<Placemark> placemarks = await placemarkFromCoordinates(
      //   position.latitude,
      //   position.longitude,
      // );

      // final placemark = placemarks.first;

      // موقع افتراضي (صنعاء، اليمن)
      final defaultLat = 15.3694;
      final defaultLng = 44.1910;

      // الحصول على بيانات المستخدم من قاعدة البيانات
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: LatLng(defaultLat, defaultLng),
        address: 'صنعاء، اليمن',
        country: 'اليمن',
        city: 'صنعاء',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser

      debugPrint('📍 تم إضافة موقع افتراضي في صنعاء');
      debugPrint('🏠 العنوان: ${_currentUser!.address}');

      return _currentUser;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المستخدم الحالي: $e');

      // إنشاء مستخدم افتراضي في صنعاء إذا فشل الحصول على الموقع
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: const LatLng(15.3694, 44.1910), // صنعاء، اليمن
        address: 'صنعاء، اليمن',
        country: 'اليمن',
        city: 'صنعاء',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser فقط

      debugPrint('📍 تم إضافة موقع افتراضي في صنعاء');

      return _currentUser;
    }
  }

  // تهيئة جميع المستخدمين من قاعدة البيانات
  Future<void> _initializeRealUser() async {
    debugPrint('🔄 بدء تهيئة المستخدمين...');
    _activeUsers.clear();

    try {
      // الحصول على المستخدم الحقيقي (المدير)
      final currentUser = await getCurrentUser();

      // جلب جميع المستخدمين من قاعدة البيانات
      final allUsers = await _loadUsersFromDatabase();

      if (currentUser != null) {
        // إضافة المستخدم الحقيقي أولاً
        _activeUsers.add(currentUser);
      }

      // إضافة باقي المستخدمين من قاعدة البيانات
      _activeUsers.addAll(allUsers);

      // إرسال التحديث فوراً
      _usersController.add(List.from(_activeUsers));

      // إرسال إشعار
      _sendNotification(
        'مرحباً بك',
        'تم تسجيل دخولك من ${currentUser?.city ?? 'موقع غير محدد'}. يوجد ${_activeUsers.length} مستخدم متصل',
        'user_login',
      );

      debugPrint('✅ تم تهيئة ${_activeUsers.length} مستخدم');
      if (currentUser != null) {
        debugPrint('👤 المدير: ${currentUser.name} من ${currentUser.city}');
      }
      debugPrint('👥 مستخدمون آخرون: ${allUsers.length}');
      debugPrint('📊 إجمالي المستخدمين النشطين: ${_activeUsers.length}');

    } catch (e) {
      debugPrint('❌ خطأ في تهيئة المستخدمين: $e');
      _usersController.add([]);
    }
  }

  // جلب المستخدمين من قاعدة البيانات مع البيانات الحقيقية
  Future<List<ActiveUserModel>> _loadUsersFromDatabase() async {
    try {
      debugPrint('🔍 🆕 جلب المستخدمين المتصلين من النظام الجديد...');

      // استخدام الخدمة العادية للحصول على البيانات من View الجديد
      final supabase = SupabaseService.instance.client;

      // جلب المستخدمين المتصلين من View المبسط
      final response = await supabase
          .from('v_simple_online_users')
          .select('*')
          .order('last_seen', ascending: false);

      debugPrint('📊 تم جلب ${response.length} مستخدم من النظام الجديد');

      List<ActiveUserModel> users = [];

      for (int i = 0; i < response.length; i++) {
        final userData = response[i];

        debugPrint('🔍 فحص المستخدم ${i + 1}:');
        debugPrint('   📧 البريد: ${userData['email']}');
        debugPrint('   👤 الاسم: ${userData['full_name']}');
        debugPrint('   🆔 ID: ${userData['id']}');
        debugPrint('   📍 الموقع: ${userData['location_name']}');
        debugPrint('   📱 الجهاز: ${userData['device_name']}');
        debugPrint('   ⏰ آخر ظهور: ${userData['last_seen']}');
        debugPrint('   🟢 الحالة: ${userData['status_text']}');

        // تخطي المستخدم الحالي (المدير) لأنه سيتم إضافته منفصلاً
        if (userData['email'] == _currentUser?.email) {
          debugPrint('   ⏭️ تم تخطي هذا المستخدم (مستخدم حالي)');
          continue;
        }

        debugPrint('   ✅ سيتم معالجة هذا المستخدم...');

        // تحويل البيانات من View الجديد
        final lastSeen = userData['last_seen'] != null
            ? DateTime.parse(userData['last_seen'])
            : DateTime.now().subtract(const Duration(hours: 1));

        final minutesSinceLastSeen = userData['minutes_since_last_seen'] as double? ?? 999;
        final isOnline = minutesSinceLastSeen < 5; // متصل إذا كان آخر نشاط خلال 5 دقائق

        // تحديد الموقع
        final locationName = userData['location_name'] as String?;
        final locationParts = locationName?.split(', ') ?? ['غير محدد', 'غير محدد'];
        final city = locationParts.isNotEmpty ? locationParts[0] : 'غير محدد';
        final country = locationParts.length > 1 ? locationParts.last : 'غير محدد';

        // جلب الإحصائيات الحقيقية
        final userStats = await _getUserRealStats(userData['id']);

        final user = ActiveUserModel(
          id: userData['id'],
          name: userData['full_name'] ?? 'مستخدم غير محدد',
          email: userData['email'] ?? '',
          location: const LatLng(15.3694, 44.1910), // موقع افتراضي - يمكن تحسينه
          address: locationName ?? '$city, $country',
          country: country,
          city: city,
          lastSeen: lastSeen,
          isOnline: isOnline,
          deviceType: userData['device_brand'] ?? 'Unknown',
          deviceModel: userData['device_model'] ?? userData['device_name'] ?? 'Unknown Device',
          batteryLevel: 100, // افتراضي
          status: _getUserStatusFromMinutes(minutesSinceLastSeen),
          privacyLevel: PrivacyLevel.public,
          stats: userStats,
        );

        users.add(user);
        debugPrint('   ✅ تم إضافة المستخدم: ${user.name} - ${user.status}');
        debugPrint('   ─────────────────────────────────────────────────────────────');
      }

      debugPrint('✅ تم إنشاء ${users.length} مستخدم نشط بالبيانات الحقيقية');
      return users;

    } catch (e) {
      debugPrint('❌ خطأ في جلب المستخدمين من قاعدة البيانات: $e');
      return [];
    }
  }





  // جلب إحصائيات المستخدم الحقيقية
  Future<UserStats> _getUserRealStats(String userId) async {
    try {
      final supabase = SupabaseService.instance.client;

      // جلب إحصائيات الصور
      final photosToday = await supabase
          .from('photos')
          .select('id')
          .eq('user_id', userId)
          .gte('upload_timestamp', DateTime.now().toIso8601String().substring(0, 10))
          .count();

      final totalPhotos = await supabase
          .from('photos')
          .select('id')
          .eq('user_id', userId)
          .count();

      // جلب إحصائيات الفيديوهات
      final videosToday = await supabase
          .from('videos')
          .select('id')
          .eq('user_id', userId)
          .gte('upload_timestamp', DateTime.now().toIso8601String().substring(0, 10))
          .count();

      final totalVideos = await supabase
          .from('videos')
          .select('id')
          .eq('user_id', userId)
          .count();

      // جلب معلومات المستخدم للحصول على تاريخ الانضمام
      final userInfo = await supabase
          .from('users')
          .select('created_at')
          .eq('id', userId)
          .single();

      return UserStats(
        photosToday: photosToday.count,
        videosToday: videosToday.count,
        totalPhotos: totalPhotos.count,
        totalVideos: totalVideos.count,
        hoursActiveToday: 0.0, // يمكن حسابها من جدول الأنشطة إذا وجد
        totalHoursActive: 0.0, // يمكن حسابها من جدول الأنشطة إذا وجد
        sessionsToday: 1, // يمكن حسابها من جدول الجلسات إذا وجد
        totalSessions: 1, // يمكن حسابها من جدول الجلسات إذا وجد
        joinDate: DateTime.parse(userInfo['created_at']),
      );

    } catch (e) {
      debugPrint('⚠️ خطأ في جلب إحصائيات المستخدم $userId: $e');
      // إرجاع إحصائيات افتراضية في حالة الخطأ
      return UserStats(
        photosToday: 0,
        videosToday: 0,
        totalPhotos: 0,
        totalVideos: 0,
        hoursActiveToday: 0.0,
        totalHoursActive: 0.0,
        sessionsToday: 0,
        totalSessions: 0,
        joinDate: DateTime.now().subtract(const Duration(days: 30)),
      );
    }
  }

  // تحديد حالة المستخدم من الدقائق منذ آخر ظهور
  UserStatus _getUserStatusFromMinutes(double minutes) {
    if (minutes < 2) {
      return UserStatus.working; // متصل الآن
    } else if (minutes < 5) {
      return UserStatus.available; // نشط مؤخراً
    } else if (minutes < 30) {
      return UserStatus.break_; // غير نشط
    } else {
      return UserStatus.offline; // غير متصل
    }
  }



  // تحديثات دورية للمستخدم الحقيقي فقط
  void _startPeriodicUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateRealUserStatus();
    });
  }

  // تحديث حالة المستخدم الحقيقي
  void _updateRealUserStatus() {
    if (_currentUser != null && _activeUsers.isNotEmpty) {
      // تحديث آخر ظهور للمستخدم الحقيقي
      final updatedUser = _currentUser!.copyWith(
        lastSeen: DateTime.now(),
        isOnline: true,
        batteryLevel: 100, // افتراض أن الكمبيوتر متصل بالكهرباء
      );

      _currentUser = updatedUser;
      _activeUsers[0] = updatedUser;
      _usersController.add(_activeUsers);

      debugPrint('🔄 تم تحديث حالة المستخدم الحقيقي');
    }
  }



  // إرسال إشعار
  void _sendNotification(String title, String message, String type, {Map<String, dynamic>? data}) {
    final notification = UserNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );
    
    _notificationsController.add(notification);
  }

  // الحصول على المستخدمين النشطين
  List<ActiveUserModel> get activeUsers => List.unmodifiable(_activeUsers);

  // الحصول على المستخدمين حسب البلد
  Map<String, List<ActiveUserModel>> getUsersByCountry() {
    final Map<String, List<ActiveUserModel>> result = {};
    
    for (final user in _activeUsers) {
      if (!result.containsKey(user.country)) {
        result[user.country] = [];
      }
      result[user.country]!.add(user);
    }
    
    return result;
  }

  // الحصول على إحصائيات عامة
  Map<String, dynamic> getGlobalStats() {
    final totalUsers = _activeUsers.length;
    final onlineUsers = _activeUsers.where((u) => u.isOnline).length;
    final activeUsers = _activeUsers.where((u) => u.isActive).length;
    final totalPhotosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.photosToday);
    final totalVideosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.videosToday);
    
    return {
      'totalUsers': totalUsers,
      'onlineUsers': onlineUsers,
      'activeUsers': activeUsers,
      'offlineUsers': totalUsers - onlineUsers,
      'totalPhotosToday': totalPhotosToday,
      'totalVideosToday': totalVideosToday,
      'totalMediaToday': totalPhotosToday + totalVideosToday,
      'countries': getUsersByCountry().length,
    };
  }
}
