import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'screens/main_layout.dart';
import 'services/supabase_service.dart';
import 'services/admin_supabase_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Supabase
  await SupabaseService.instance.initialize();

  // تهيئة الخدمة الإدارية
  await AdminSupabaseService.instance.initialize();

  runApp(const ProviderScope(child: MoonMemoryAdminApp()));
}

class MoonMemoryAdminApp extends StatelessWidget {
  const MoonMemoryAdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.darkTheme,
      debugShowCheckedModeBanner: false,
      home: const MainLayout(),
    );
  }
}


