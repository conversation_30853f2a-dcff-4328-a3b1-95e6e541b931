class AppConstants {
  // App Info
  static const String appName = 'Moon Memory Admin';
  static const String appVersion = '1.0.0';
  
  // Supabase Configuration
  static const String supabaseUrl = 'https://xufiuvdtfusbaerwrkzb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1Zml1dmR0ZnVzYmFlcndya3piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzNDA4MjUsImV4cCI6MjA1MDkxNjgyNX0.utjAcWoqFp0DD4IYt9Z-mVOmaLSxoh4yj_frLvOzfrE';

  // Service Role Key للعمليات الإدارية (إنشاء المستخدمين)
  static const String supabaseServiceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1Zml1dmR0ZnVzYmFlcndya3piIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTM0MDgyNSwiZXhwIjoyMDUwOTE2ODI1fQ.bZJ59MOSwLM9N3PvaFdRWAtNN-6iTBG5OAnGJLT_h4A';
  
  // Locations
  static const List<String> uLocations = [
    'U101', 'U102', 'U103', 'U104', 'U105', 'U106', 'U107', 'U108', 'U109', 'U110',
    'U111', 'U112', 'U113', 'U114', 'U115', 'U116', 'U117', 'U118', 'U119', 'U120',
    'U121', 'U122', 'U123', 'U124', 'U125'
  ];
  
  static const List<String> cLocations = [
    'C101', 'C102', 'C103', 'C104', 'C105', 'C106', 'C107', 'C108', 'C109', 'C110',
    'C111', 'C112', 'C113', 'C114', 'C115', 'C116', 'C117', 'C118', 'C119', 'C120',
    'C121', 'C122', 'C123', 'C124', 'C125', 'C126', 'C127', 'C128', 'C129', 'C130',
    'C131', 'C132', 'C133', 'C134', 'C135', 'C136', 'C137', 'C138', 'C139', 'C140',
    'C141', 'C142', 'C143', 'C144', 'C145'
  ];
  
  static List<String> get allLocations => [...uLocations, ...cLocations];
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Sizes
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxVideoSize = 100 * 1024 * 1024; // 100MB
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Cache Settings
  static const Duration cacheExpiry = Duration(minutes: 30);
  static const int maxCacheSize = 100;
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayDateTimeFormat = 'dd/MM/yyyy HH:mm';
}
