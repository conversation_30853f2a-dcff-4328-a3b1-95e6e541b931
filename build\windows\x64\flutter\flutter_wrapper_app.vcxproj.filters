﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\windows\flutter\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{915BF2C6-7B3D-3F7F-9696-9386903CACC6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{18DCB59C-0984-3450-847E-78F8DD085E9F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
