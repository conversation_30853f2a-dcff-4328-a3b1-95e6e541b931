import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/users_provider.dart';
import '../../models/user_model.dart';
import 'user_details_dialog.dart';

class UsersDataTable extends ConsumerWidget {
  const UsersDataTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersState = ref.watch(usersProvider);

    if (usersState.users.isEmpty && !usersState.isLoading) {
      return _buildEmptyState(context);
    }

    return Column(
      children: [
        // الجدول
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              // حساب العرض المتاح بناءً على حجم الحاوي الفعلي
              final availableWidth = constraints.maxWidth;
              final tableWidth = availableWidth > 1200 ? availableWidth : 1200.0;

              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SingleChildScrollView(
                  child: SizedBox(
                    width: tableWidth,
                    child: DataTable(
                      columnSpacing: availableWidth > 1000 ? 12 : 8,
                      horizontalMargin: availableWidth > 1000 ? 16 : 12,
                      headingRowHeight: 52,
                      dataRowMinHeight: 64,
                      dataRowMaxHeight: 72,
                      headingRowColor: WidgetStateProperty.all(
                        AppColors.cardBackground.withValues(alpha: 0.5),
                      ),
                      dividerThickness: 1,
                      showBottomBorder: true,
                columns: [
                  DataColumn(
                    label: SizedBox(
                      width: availableWidth > 1000 ? 180 : 150,
                      child: Text('المستخدم', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.primaryText)),
                    ),
                    tooltip: 'معلومات المستخدم الأساسية',
                  ),

                  DataColumn(
                    label: SizedBox(
                      width: availableWidth > 1000 ? 100 : 80,
                      child: Text('القسم', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.primaryText)),
                    ),
                  ),
                  DataColumn(
                    label: SizedBox(
                      width: availableWidth > 1000 ? 100 : 80,
                      child: Text('المنصب', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.primaryText)),
                    ),
                  ),
                  DataColumn(
                    label: SizedBox(
                      width: availableWidth > 1000 ? 80 : 70,
                      child: Text('الحالة', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.primaryText)),
                    ),
                  ),
                  DataColumn(
                    label: SizedBox(
                      width: availableWidth > 1000 ? 80 : 70,
                      child: Text('النوع', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.primaryText)),
                    ),
                  ),
                  DataColumn(
                    label: SizedBox(
                      width: availableWidth > 1000 ? 120 : 100,
                      child: Text('آخر دخول', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.primaryText)),
                    ),
                  ),
                  DataColumn(
                    label: SizedBox(
                      width: availableWidth > 1000 ? 160 : 140,
                      child: Text('الإجراءات', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.primaryText)),
                    ),
                  ),
                ],
                rows: usersState.users.map((user) {
                  return DataRow(
                    color: WidgetStateProperty.resolveWith<Color?>(
                      (Set<WidgetState> states) {
                        if (states.contains(WidgetState.hovered)) {
                          return AppColors.primaryGold.withValues(alpha: 0.05);
                        }
                        return null;
                      },
                    ),
                    cells: [
                      // معلومات المستخدم
                      DataCell(
                        SizedBox(
                          width: availableWidth > 1000 ? 180 : 150,
                          child: Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: AppColors.primaryGold,
                                radius: 18,
                                child: Text(
                                  user.fullName?.substring(0, 1) ?? 'م',
                                  style: const TextStyle(
                                    color: AppColors.darkBackground,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      user.fullName ?? 'غير محدد',
                                      style: const TextStyle(
                                        color: AppColors.primaryText,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 13,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    if (user.nationalId != null)
                                      Text(
                                        user.nationalId!,
                                        style: const TextStyle(
                                          color: AppColors.secondaryText,
                                          fontSize: 11,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        onTap: () => _showUserDetails(context, user),
                      ),


                      // القسم
                      DataCell(
                        SizedBox(
                          width: availableWidth > 1000 ? 100 : 80,
                          child: Text(
                            user.department ?? 'غير محدد',
                            style: const TextStyle(
                              color: AppColors.primaryText,
                              fontSize: 12,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      
                      // المنصب
                      DataCell(
                        SizedBox(
                          width: availableWidth > 1000 ? 100 : 80,
                          child: Text(
                            user.position ?? 'غير محدد',
                            style: const TextStyle(
                              color: AppColors.primaryText,
                              fontSize: 12,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                      // الحالة
                      DataCell(
                        SizedBox(
                          width: availableWidth > 1000 ? 80 : 70,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: user.isActive
                                  ? AppColors.success.withValues(alpha: 0.1)
                                  : AppColors.error.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: user.isActive
                                    ? AppColors.success
                                    : AppColors.error,
                                width: 1,
                              ),
                            ),
                            child: Text(
                              user.isActive ? 'نشط' : 'غير نشط',
                              style: TextStyle(
                                color: user.isActive
                                    ? AppColors.success
                                    : AppColors.error,
                                fontWeight: FontWeight.w600,
                                fontSize: 11,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                      
                      // النوع
                      DataCell(
                        SizedBox(
                          width: availableWidth > 1000 ? 80 : 70,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: user.isAdmin
                                  ? AppColors.primaryGold.withValues(alpha: 0.1)
                                  : AppColors.info.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: user.isAdmin
                                    ? AppColors.primaryGold
                                    : AppColors.info,
                                width: 1,
                              ),
                            ),
                            child: Text(
                              user.isAdmin ? 'مدير' : 'مستخدم',
                              style: TextStyle(
                                color: user.isAdmin
                                    ? AppColors.primaryGold
                                    : AppColors.info,
                                fontWeight: FontWeight.w600,
                                fontSize: 11,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),

                      // آخر دخول
                      DataCell(
                        SizedBox(
                          width: availableWidth > 1000 ? 120 : 100,
                          child: Text(
                            _formatDate(user.lastLogin),
                            style: const TextStyle(
                              color: AppColors.secondaryText,
                              fontSize: 11,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      
                      // الإجراءات
                      DataCell(
                        SizedBox(
                          width: availableWidth > 1000 ? 160 : 140,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                onPressed: () => _showUserDetails(context, user),
                                icon: Icon(Icons.visibility, size: availableWidth > 1000 ? 18 : 16),
                                tooltip: 'عرض التفاصيل',
                                padding: EdgeInsets.all(availableWidth > 1000 ? 6 : 4),
                                constraints: BoxConstraints(
                                  minWidth: availableWidth > 1000 ? 36 : 32,
                                  minHeight: availableWidth > 1000 ? 36 : 32
                                ),
                                color: AppColors.info,
                                style: IconButton.styleFrom(
                                  backgroundColor: AppColors.info.withValues(alpha: 0.1),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                ),
                              ),
                              SizedBox(width: availableWidth > 1000 ? 4 : 2),
                              IconButton(
                                onPressed: () => _toggleUserStatus(context, ref, user),
                                icon: Icon(
                                  user.isActive ? Icons.block : Icons.check_circle,
                                  size: availableWidth > 1000 ? 18 : 16,
                                ),
                                tooltip: user.isActive ? 'إلغاء التفعيل' : 'تفعيل',
                                padding: EdgeInsets.all(availableWidth > 1000 ? 6 : 4),
                                constraints: BoxConstraints(
                                  minWidth: availableWidth > 1000 ? 36 : 32,
                                  minHeight: availableWidth > 1000 ? 36 : 32
                                ),
                                color: user.isActive ? AppColors.warning : AppColors.success,
                                style: IconButton.styleFrom(
                                  backgroundColor: (user.isActive ? AppColors.warning : AppColors.success).withValues(alpha: 0.1),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                ),
                              ),
                              SizedBox(width: availableWidth > 1000 ? 4 : 2),
                              IconButton(
                                onPressed: () => _changePassword(context, ref, user),
                                icon: Icon(Icons.lock_reset, size: availableWidth > 1000 ? 18 : 16),
                                tooltip: 'تغيير كلمة المرور',
                                padding: EdgeInsets.all(availableWidth > 1000 ? 6 : 4),
                                constraints: BoxConstraints(
                                  minWidth: availableWidth > 1000 ? 36 : 32,
                                  minHeight: availableWidth > 1000 ? 36 : 32
                                ),
                                color: AppColors.warning,
                                style: IconButton.styleFrom(
                                  backgroundColor: AppColors.warning.withValues(alpha: 0.1),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                ),
                              ),
                              SizedBox(width: availableWidth > 1000 ? 4 : 2),
                              IconButton(
                                onPressed: () => _deleteUser(context, ref, user),
                                icon: Icon(Icons.delete, size: availableWidth > 1000 ? 18 : 16),
                                tooltip: 'حذف',
                                padding: EdgeInsets.all(availableWidth > 1000 ? 6 : 4),
                                constraints: BoxConstraints(
                                  minWidth: availableWidth > 1000 ? 36 : 32,
                                  minHeight: availableWidth > 1000 ? 36 : 32
                                ),
                                color: AppColors.error,
                                style: IconButton.styleFrom(
                                  backgroundColor: AppColors.error.withValues(alpha: 0.1),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        
        // شريط التصفح
        _buildPaginationBar(context, ref, usersState),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: AppColors.mutedText,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مستخدمين',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.mutedText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أي مستخدمين مطابقين للفلاتر المحددة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaginationBar(BuildContext context, WidgetRef ref, UsersState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Text(
            'عرض ${((state.currentPage - 1) * state.pageSize) + 1} - '
            '${(state.currentPage * state.pageSize).clamp(0, state.totalCount)} '
            'من ${state.totalCount}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.secondaryText,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: state.hasPreviousPage 
                ? () => ref.read(usersProvider.notifier).previousPage()
                : null,
            icon: const Icon(Icons.chevron_left),
            tooltip: 'الصفحة السابقة',
          ),
          Text(
            '${state.currentPage} من ${state.totalPages}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.primaryText,
            ),
          ),
          IconButton(
            onPressed: state.hasNextPage 
                ? () => ref.read(usersProvider.notifier).nextPage()
                : null,
            icon: const Icon(Icons.chevron_right),
            tooltip: 'الصفحة التالية',
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'لم يسجل دخول';
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  void _showUserDetails(BuildContext context, UserModel user) {
    showDialog(
      context: context,
      builder: (context) => UserDetailsDialog(user: user),
    );
  }

  void _toggleUserStatus(BuildContext context, WidgetRef ref, UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(user.isActive ? 'إلغاء تفعيل المستخدم' : 'تفعيل المستخدم'),
        content: Text(
          user.isActive 
              ? 'هل أنت متأكد من إلغاء تفعيل ${user.fullName}؟'
              : 'هل أنت متأكد من تفعيل ${user.fullName}؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(usersProvider.notifier).toggleUserStatus(user.id, !user.isActive);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          Icon(
                            user.isActive ? Icons.block : Icons.check_circle,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            user.isActive
                                ? 'تم إلغاء تفعيل ${user.fullName}'
                                : 'تم تفعيل ${user.fullName}',
                          ),
                        ],
                      ),
                      backgroundColor: user.isActive ? AppColors.warning : AppColors.success,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(Icons.error, color: Colors.white),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text('خطأ: لا يمكن تغيير حالة المستخدم. تحقق من الصلاحيات.'),
                          ),
                        ],
                      ),
                      backgroundColor: AppColors.error,
                      duration: const Duration(seconds: 4),
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: user.isActive ? AppColors.warning : AppColors.success,
            ),
            child: Text(user.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
          ),
        ],
      ),
    );
  }

  void _changePassword(BuildContext context, WidgetRef ref, UserModel user) {
    final passwordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lock_reset, color: AppColors.warning),
            const SizedBox(width: 8),
            const Text('تغيير كلمة المرور'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'تغيير كلمة المرور للمستخدم: ${user.fullName}',
                  style: TextStyle(color: AppColors.secondaryText),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: passwordController,
                  decoration: const InputDecoration(
                    labelText: 'كلمة المرور الجديدة',
                    prefixIcon: Icon(Icons.lock),
                    helperText: 'يجب أن تحتوي على 8 أحرف على الأقل',
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value?.isEmpty ?? true) return 'كلمة المرور مطلوبة';
                    if (value!.length < 8) return 'كلمة المرور قصيرة جداً';
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: confirmPasswordController,
                  decoration: const InputDecoration(
                    labelText: 'تأكيد كلمة المرور',
                    prefixIcon: Icon(Icons.lock_outline),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value?.isEmpty ?? true) return 'تأكيد كلمة المرور مطلوب';
                    if (value != passwordController.text) return 'كلمات المرور غير متطابقة';
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState?.validate() ?? false) {
                Navigator.of(context).pop();

                // إظهار مؤشر التحميل
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                );

                try {
                  // تغيير كلمة المرور
                  final result = await ref.read(adminSupabaseServiceProvider).resetPassword(
                    userId: user.id,
                    newPassword: passwordController.text,
                  );

                  // إغلاق مؤشر التحميل
                  if (context.mounted) Navigator.of(context).pop();

                  if (result['success'] == true) {
                    // إظهار رسالة نجاح
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              const Icon(Icons.check_circle, color: Colors.white),
                              const SizedBox(width: 8),
                              Text('تم تغيير كلمة المرور لـ ${user.fullName}'),
                            ],
                          ),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  } else {
                    // إظهار رسالة خطأ
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              const Icon(Icons.error, color: Colors.white),
                              const SizedBox(width: 8),
                              Text('فشل في تغيير كلمة المرور: ${result['error'] ?? 'خطأ غير معروف'}'),
                            ],
                          ),
                          backgroundColor: AppColors.error,
                        ),
                      );
                    }
                  }
                } catch (error) {
                  // إغلاق مؤشر التحميل
                  if (context.mounted) Navigator.of(context).pop();

                  // إظهار رسالة خطأ
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            const Icon(Icons.error, color: Colors.white),
                            const SizedBox(width: 8),
                            Text('خطأ في تغيير كلمة المرور: ${error.toString()}'),
                          ],
                        ),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.warning),
            child: const Text('تغيير كلمة المرور'),
          ),
        ],
      ),
    );
  }

  void _deleteUser(BuildContext context, WidgetRef ref, UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المستخدم'),
        content: Text('هل أنت متأكد من حذف ${user.fullName}؟ هذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(usersProvider.notifier).deleteUser(user.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(Icons.delete, color: Colors.white),
                          const SizedBox(width: 8),
                          Text('تم حذف ${user.fullName} بنجاح'),
                        ],
                      ),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(Icons.error, color: Colors.white),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text('خطأ: لا يمكن حذف المستخدم. تحقق من الصلاحيات.'),
                          ),
                        ],
                      ),
                      backgroundColor: AppColors.error,
                      duration: const Duration(seconds: 4),
                      behavior: SnackBarBehavior.floating,
                      action: SnackBarAction(
                        label: 'إغلاق',
                        textColor: Colors.white,
                        onPressed: () {
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        },
                      ),
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('حذف نهائياً'),
          ),
        ],
      ),
    );
  }
}
