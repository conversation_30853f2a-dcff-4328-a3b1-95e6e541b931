import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../core/constants/app_colors.dart';

class ChartCard extends StatelessWidget {
  final int totalPhotos;
  final int totalVideos;

  const ChartCard({
    super.key,
    required this.totalPhotos,
    required this.totalVideos,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.pie_chart,
                  color: AppColors.primaryGold,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'توزيع المحتوى',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.primaryGold,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // الرسم البياني
            SizedBox(
              height: 200,
              child: totalPhotos == 0 && totalVideos == 0
                  ? _buildEmptyChart(context)
                  : _buildPieChart(),
            ),
            
            const SizedBox(height: 16),
            
            // المفاتيح
            _buildLegend(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    final total = totalPhotos + totalVideos;
    if (total == 0) return _buildEmptyChart(null);

    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 60,
        sections: [
          PieChartSectionData(
            color: AppColors.success,
            value: totalPhotos.toDouble(),
            title: '${((totalPhotos / total) * 100).toStringAsFixed(1)}%',
            radius: 80,
            titleStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          PieChartSectionData(
            color: AppColors.warning,
            value: totalVideos.toDouble(),
            title: '${((totalVideos / total) * 100).toStringAsFixed(1)}%',
            radius: 80,
            titleStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChart(BuildContext? context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pie_chart_outline,
            size: 48,
            color: AppColors.mutedText,
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد بيانات للعرض',
            style: TextStyle(
              color: AppColors.mutedText,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegend(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildLegendItem(
          context,
          color: AppColors.success,
          label: 'الصور',
          value: totalPhotos,
        ),
        _buildLegendItem(
          context,
          color: AppColors.warning,
          label: 'الفيديوهات',
          value: totalVideos,
        ),
      ],
    );
  }

  Widget _buildLegendItem(
    BuildContext context, {
    required Color color,
    required String label,
    required int value,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.secondaryText,
              ),
            ),
            Text(
              value.toString(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.primaryText,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
