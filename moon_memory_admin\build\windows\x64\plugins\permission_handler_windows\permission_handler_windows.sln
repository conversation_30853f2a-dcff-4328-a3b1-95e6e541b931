﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{C6B9AA44-C358-33C0-90FA-6417E30EF566}"
	ProjectSection(ProjectDependencies) = postProject
		{********-62BB-3A53-A41E-0C81E34DD81F} = {********-62BB-3A53-A41E-0C81E34DD81F}
		{F9D91351-2C5C-3A24-A38C-B1664CF29155} = {F9D91351-2C5C-3A24-A38C-B1664CF29155}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{8179AC84-47C4-3637-8594-E8563BA3FE56}"
	ProjectSection(ProjectDependencies) = postProject
		{C6B9AA44-C358-33C0-90FA-6417E30EF566} = {C6B9AA44-C358-33C0-90FA-6417E30EF566}
		{********-62BB-3A53-A41E-0C81E34DD81F} = {********-62BB-3A53-A41E-0C81E34DD81F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{********-62BB-3A53-A41E-0C81E34DD81F}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}"
	ProjectSection(ProjectDependencies) = postProject
		{********-62BB-3A53-A41E-0C81E34DD81F} = {********-62BB-3A53-A41E-0C81E34DD81F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{2B17AEB8-E140-332E-83A0-0B29EAD2D143}"
	ProjectSection(ProjectDependencies) = postProject
		{********-62BB-3A53-A41E-0C81E34DD81F} = {********-62BB-3A53-A41E-0C81E34DD81F}
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D} = {253B2A59-4BAC-3DD4-A86E-1923D0D9106D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "permission_handler_windows_plugin", "permission_handler_windows_plugin.vcxproj", "{F9D91351-2C5C-3A24-A38C-B1664CF29155}"
	ProjectSection(ProjectDependencies) = postProject
		{********-62BB-3A53-A41E-0C81E34DD81F} = {********-62BB-3A53-A41E-0C81E34DD81F}
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D} = {253B2A59-4BAC-3DD4-A86E-1923D0D9106D}
		{2B17AEB8-E140-332E-83A0-0B29EAD2D143} = {2B17AEB8-E140-332E-83A0-0B29EAD2D143}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C6B9AA44-C358-33C0-90FA-6417E30EF566}.Debug|x64.ActiveCfg = Debug|x64
		{C6B9AA44-C358-33C0-90FA-6417E30EF566}.Debug|x64.Build.0 = Debug|x64
		{C6B9AA44-C358-33C0-90FA-6417E30EF566}.Profile|x64.ActiveCfg = Profile|x64
		{C6B9AA44-C358-33C0-90FA-6417E30EF566}.Profile|x64.Build.0 = Profile|x64
		{C6B9AA44-C358-33C0-90FA-6417E30EF566}.Release|x64.ActiveCfg = Release|x64
		{C6B9AA44-C358-33C0-90FA-6417E30EF566}.Release|x64.Build.0 = Release|x64
		{8179AC84-47C4-3637-8594-E8563BA3FE56}.Debug|x64.ActiveCfg = Debug|x64
		{8179AC84-47C4-3637-8594-E8563BA3FE56}.Profile|x64.ActiveCfg = Profile|x64
		{8179AC84-47C4-3637-8594-E8563BA3FE56}.Release|x64.ActiveCfg = Release|x64
		{********-62BB-3A53-A41E-0C81E34DD81F}.Debug|x64.ActiveCfg = Debug|x64
		{********-62BB-3A53-A41E-0C81E34DD81F}.Debug|x64.Build.0 = Debug|x64
		{********-62BB-3A53-A41E-0C81E34DD81F}.Profile|x64.ActiveCfg = Profile|x64
		{********-62BB-3A53-A41E-0C81E34DD81F}.Profile|x64.Build.0 = Profile|x64
		{********-62BB-3A53-A41E-0C81E34DD81F}.Release|x64.ActiveCfg = Release|x64
		{********-62BB-3A53-A41E-0C81E34DD81F}.Release|x64.Build.0 = Release|x64
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}.Debug|x64.ActiveCfg = Debug|x64
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}.Debug|x64.Build.0 = Debug|x64
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}.Profile|x64.ActiveCfg = Profile|x64
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}.Profile|x64.Build.0 = Profile|x64
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}.Release|x64.ActiveCfg = Release|x64
		{253B2A59-4BAC-3DD4-A86E-1923D0D9106D}.Release|x64.Build.0 = Release|x64
		{2B17AEB8-E140-332E-83A0-0B29EAD2D143}.Debug|x64.ActiveCfg = Debug|x64
		{2B17AEB8-E140-332E-83A0-0B29EAD2D143}.Debug|x64.Build.0 = Debug|x64
		{2B17AEB8-E140-332E-83A0-0B29EAD2D143}.Profile|x64.ActiveCfg = Profile|x64
		{2B17AEB8-E140-332E-83A0-0B29EAD2D143}.Profile|x64.Build.0 = Profile|x64
		{2B17AEB8-E140-332E-83A0-0B29EAD2D143}.Release|x64.ActiveCfg = Release|x64
		{2B17AEB8-E140-332E-83A0-0B29EAD2D143}.Release|x64.Build.0 = Release|x64
		{F9D91351-2C5C-3A24-A38C-B1664CF29155}.Debug|x64.ActiveCfg = Debug|x64
		{F9D91351-2C5C-3A24-A38C-B1664CF29155}.Debug|x64.Build.0 = Debug|x64
		{F9D91351-2C5C-3A24-A38C-B1664CF29155}.Profile|x64.ActiveCfg = Profile|x64
		{F9D91351-2C5C-3A24-A38C-B1664CF29155}.Profile|x64.Build.0 = Profile|x64
		{F9D91351-2C5C-3A24-A38C-B1664CF29155}.Release|x64.ActiveCfg = Release|x64
		{F9D91351-2C5C-3A24-A38C-B1664CF29155}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4CEC1E5F-B978-3E54-9FDF-46F559206766}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
