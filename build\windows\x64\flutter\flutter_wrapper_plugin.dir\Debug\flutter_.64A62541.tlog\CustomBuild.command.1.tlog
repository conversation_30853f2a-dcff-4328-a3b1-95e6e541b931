^C:\USERS\<USER>\CASCADEPROJECTS\MOON_MEMORY_V2\MOON_MEMORY_ADMIN\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
