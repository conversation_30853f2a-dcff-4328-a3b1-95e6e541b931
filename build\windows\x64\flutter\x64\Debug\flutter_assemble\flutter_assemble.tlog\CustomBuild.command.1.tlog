^C:\USERS\<USER>\CASCADEPROJECTS\MOON_MEMORY_V2\MOON_MEMORY_ADMIN\BUILD\WINDOWS\X64\CMAKEFILES\E8143EDDBF79CDCABA76F7211B0AEB03\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin FLUTTER_TARGET=C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CASCADEPROJECTS\MOON_MEMORY_V2\MOON_MEMORY_ADMIN\BUILD\WINDOWS\X64\CMAKEFILES\FDDC655509328AEAE762AF62096D180F\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CASCADEPROJECTS\MOON_MEMORY_V2\MOON_MEMORY_ADMIN\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/build/windows/x64 --check-stamp-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
