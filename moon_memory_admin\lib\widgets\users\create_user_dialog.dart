import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/users_provider.dart';

class CreateUserDialog extends ConsumerStatefulWidget {
  const CreateUserDialog({super.key});

  @override
  ConsumerState<CreateUserDialog> createState() => _CreateUserDialogState();
}

class _CreateUserDialogState extends ConsumerState<CreateUserDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nationalIdController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _departmentController = TextEditingController();
  final _positionController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isAdmin = false;
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _passwordController.text = _generateRandomPassword();
    // مسح رسالة الخطأ عند فتح النافذة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(usersProvider.notifier).clearError();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس النافذة
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.primaryGold.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.person_add, color: AppColors.primaryGold, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'إنشاء حساب مستخدم جديد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryText,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                ],
              ),
            ),
            
            // محتوى النافذة
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات أساسية
                      _buildSectionTitle('المعلومات الأساسية'),
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          // الرقم الوطني
                          Expanded(
                            child: TextFormField(
                              controller: _nationalIdController,
                              decoration: const InputDecoration(
                                labelText: 'الرقم الوطني *',
                                prefixIcon: Icon(Icons.badge),
                                hintText: '1234567890',
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value?.isEmpty ?? true) return 'الرقم الوطني مطلوب';
                                if (value!.length != 10) return 'يجب أن يكون 10 أرقام';
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          
                          // الاسم الكامل
                          Expanded(
                            flex: 2,
                            child: TextFormField(
                              controller: _fullNameController,
                              decoration: const InputDecoration(
                                labelText: 'الاسم الكامل *',
                                prefixIcon: Icon(Icons.person),
                                hintText: 'أحمد محمد علي',
                              ),
                              validator: (value) {
                                if (value?.isEmpty ?? true) return 'الاسم الكامل مطلوب';
                                if (value!.length < 3) return 'الاسم قصير جداً';
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // رقم الهاتف
                      TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الهاتف',
                          prefixIcon: Icon(Icons.phone),
                          hintText: '777123456',
                        ),
                        keyboardType: TextInputType.phone,
                      ),
                      const SizedBox(height: 24),
                      
                      // معلومات العمل
                      _buildSectionTitle('معلومات العمل'),
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          // القسم
                          Expanded(
                            child: TextFormField(
                              controller: _departmentController,
                              decoration: const InputDecoration(
                                labelText: 'القسم *',
                                prefixIcon: Icon(Icons.business),
                                hintText: 'قسم تقنية المعلومات',
                              ),
                              validator: (value) {
                                if (value?.isEmpty ?? true) return 'القسم مطلوب';
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          
                          // المنصب
                          Expanded(
                            child: TextFormField(
                              controller: _positionController,
                              decoration: const InputDecoration(
                                labelText: 'المنصب *',
                                prefixIcon: Icon(Icons.work),
                                hintText: 'مطور برمجيات',
                              ),
                              validator: (value) {
                                if (value?.isEmpty ?? true) return 'المنصب مطلوب';
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      
                      // كلمة المرور
                      _buildSectionTitle('كلمة المرور'),
                      const SizedBox(height: 16),
                      
                      TextFormField(
                        controller: _passwordController,
                        decoration: const InputDecoration(
                          labelText: 'كلمة المرور *',
                          prefixIcon: Icon(Icons.lock),
                          hintText: 'كلمة مرور قوية',
                          helperText: 'يجب أن تحتوي على 8 أحرف على الأقل',
                        ),
                        obscureText: true,
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'كلمة المرور مطلوبة';
                          if (value!.length < 8) return 'كلمة المرور قصيرة جداً';
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      
                      // الصلاحيات
                      _buildSectionTitle('الصلاحيات والحالة'),
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            child: Card(
                              color: _isAdmin 
                                  ? AppColors.primaryGold.withValues(alpha: 0.1)
                                  : AppColors.cardBackground,
                              child: CheckboxListTile(
                                title: const Text('مدير النظام'),
                                subtitle: const Text('صلاحيات إدارية كاملة'),
                                value: _isAdmin,
                                onChanged: (value) => setState(() => _isAdmin = value ?? false),
                                secondary: Icon(
                                  Icons.admin_panel_settings,
                                  color: _isAdmin ? AppColors.primaryGold : AppColors.secondaryText,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Card(
                              color: _isActive 
                                  ? AppColors.success.withValues(alpha: 0.1)
                                  : AppColors.error.withValues(alpha: 0.1),
                              child: CheckboxListTile(
                                title: const Text('حساب نشط'),
                                subtitle: const Text('يمكن تسجيل الدخول'),
                                value: _isActive,
                                onChanged: (value) => setState(() => _isActive = value ?? true),
                                secondary: Icon(
                                  _isActive ? Icons.check_circle : Icons.block,
                                  color: _isActive ? AppColors.success : AppColors.error,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // أزرار الإجراءات
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.surfaceBackground,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  const Spacer(),
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _createUser,
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label: Text(_isLoading ? 'جاري الإنشاء...' : 'إنشاء الحساب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryGold,
                      foregroundColor: AppColors.darkBackground,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: AppColors.primaryText,
      ),
    );
  }

  Future<void> _createUser() async {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    setState(() => _isLoading = true);

    try {
      final result = await ref.read(usersProvider.notifier).createUser(
        nationalId: _nationalIdController.text,
        fullName: _fullNameController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        department: _departmentController.text,
        position: _positionController.text,
        password: _passwordController.text,
        isAdmin: _isAdmin,
        isActive: _isActive,
      );

      if (!mounted) return;

      if (result['success']) {
        // إغلاق النافذة أولاً
        Navigator.pop(context);

        // عرض بيانات تسجيل الدخول
        _showLoginCredentials(result);

        // عرض رسالة النجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('تم إنشاء الحساب بنجاح'),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        // عرض رسالة الخطأ دون إغلاق النافذة
        _showErrorDialog(result['error'] ?? 'خطأ غير معروف');
      }
    } catch (e) {
      if (mounted) {
        // عرض رسالة الخطأ دون إغلاق النافذة
        _showErrorDialog('خطأ في إنشاء الحساب: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _generateRandomPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return String.fromCharCodes(Iterable.generate(
      8, (_) => chars.codeUnitAt(random % chars.length)));
  }

  void _showErrorDialog(String errorMessage) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: AppColors.error, size: 24),
            const SizedBox(width: 8),
            const Text('خطأ في إنشاء الحساب'),
          ],
        ),
        content: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.error),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: AppColors.error, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        errorMessage,
                        style: TextStyle(
                          color: AppColors.error,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: AppColors.info, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يمكنك تصحيح البيانات والمحاولة مرة أخرى',
                        style: TextStyle(
                          color: AppColors.info,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showLoginCredentials(Map<String, dynamic> result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.account_circle, color: AppColors.success, size: 24),
            const SizedBox(width: 8),
            const Text('بيانات تسجيل الدخول'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.success),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تم إنشاء الحساب بنجاح! 🎉',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.success,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'بيانات تسجيل الدخول للمستخدم:',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryText,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // الرقم الوطني
              _buildCredentialRow(
                'الرقم الوطني:',
                _nationalIdController.text,
                Icons.badge,
              ),
              const SizedBox(height: 12),

              // كلمة المرور
              _buildCredentialRow(
                'كلمة المرور:',
                result['password'] ?? '',
                Icons.lock,
              ),
              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: AppColors.warning, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يرجى حفظ هذه البيانات وإرسالها للمستخدم. سيستخدم الرقم الوطني وكلمة المرور لتسجيل الدخول',
                        style: TextStyle(
                          color: AppColors.warning,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // نسخ البيانات للحافظة
              // TODO: نسخ للحافظة
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('تم نسخ البيانات'),
                  backgroundColor: AppColors.info,
                ),
              );
            },
            child: const Text('نسخ البيانات'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryGold,
              foregroundColor: AppColors.darkBackground,
            ),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Widget _buildCredentialRow(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primaryGold, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    color: AppColors.primaryText,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: نسخ القيمة للحافظة
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم نسخ $label'),
                  backgroundColor: AppColors.info,
                ),
              );
            },
            icon: Icon(Icons.copy, size: 16),
            tooltip: 'نسخ',
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _nationalIdController.dispose();
    _fullNameController.dispose();
    _phoneController.dispose();
    _departmentController.dispose();
    _positionController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
