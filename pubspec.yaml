name: moon_memory_admin
description: "تطبيق إدارة شامل لنظام Moon Memory"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.3.5

  # Database & Backend
  supabase_flutter: ^2.9.1

  # UI & Design
  material_symbols_icons: ^4.2785.1
  google_fonts: ^6.2.1
  fl_chart: ^0.68.0
  cached_network_image: ^3.3.1
  photo_view: ^0.15.0
  video_player: ^2.8.6
  chewie: ^1.8.1

  # Utilities
  intl: ^0.19.0
  path_provider: ^2.1.3
  shared_preferences: ^2.2.3
  url_launcher: ^6.2.6
  file_picker: ^8.0.3
  # permission_handler: ^11.3.1  # مؤقتاً معطل

  # Maps & Location
  google_maps_flutter: ^2.5.0
  flutter_map: ^6.1.0
  latlong2: ^0.9.1
  # geolocator: ^10.1.0  # مؤقتاً معطل
  # geocoding: ^2.1.1    # مؤقتاً معطل

  # Export & Reports
  pdf: ^3.10.8
  excel: ^4.0.2
  # printing: ^5.12.0  # مُعطلة مؤقتاً بسبب مشاكل Windows

  # Animations & UI Enhancements
  animations: ^2.0.11
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # Icons
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  riverpod_generator: ^2.4.0
  build_runner: ^2.4.9
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true

  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - assets/animations/

  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Cairo-Light.ttf
  #         weight: 300
