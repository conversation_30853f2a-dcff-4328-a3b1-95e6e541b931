# 🗄️ دليل إعداد قاعدة البيانات لتتبع المستخدمين المتصلين

## 📋 **الخطوة 1: تنفيذ ملف SQL**

### 🚀 **الطريقة الأولى: Supabase Dashboard**

1. **افتح Supabase Dashboard**
   - اذهب إلى [supabase.com](https://supabase.com)
   - سجل دخولك إلى مشروعك

2. **افتح SQL Editor**
   - من القائمة الجانبية، اختر "SQL Editor"
   - اضغط على "New query"

3. **انسخ والصق الكود**
   - افتح ملف `create_user_tracking_tables.sql`
   - انسخ المحتوى كاملاً
   - الصقه في SQL Editor

4. **شغل الكود**
   - اضغط على "Run" أو `Ctrl+Enter`
   - انتظر حتى ينتهي التنفيذ

### 🖥️ **الطريقة الثانية: psql (للمطورين المتقدمين)**

```bash
# تأكد من تثبيت PostgreSQL client
psql -h your-supabase-host -U postgres -d postgres -f create_user_tracking_tables.sql
```

---

## ✅ **الخطوة 2: التحقق من النجاح**

### 🔍 **فحص الجداول المنشأة:**

```sql
-- تشغيل هذا الاستعلام للتأكد من إنشاء الجداول
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('user_sessions', 'user_activity_log') THEN '✅ جديد'
        WHEN table_name = 'users' THEN '🔄 محدث'
        ELSE '📋 موجود'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'user_sessions', 'user_activity_log')
ORDER BY table_name;
```

### 📊 **فحص الأعمدة الجديدة في جدول users:**

```sql
-- التأكد من إضافة الأعمدة الجديدة
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('last_seen', 'is_online', 'current_session_id', 'total_sessions')
ORDER BY column_name;
```

### 🧪 **اختبار الدوال:**

```sql
-- اختبار دالة الحصول على المستخدمين المتصلين
SELECT * FROM get_online_users(5);

-- اختبار View المستخدمين المتصلين
SELECT * FROM v_online_users;
```

---

## 🎯 **الخطوة 3: النتائج المتوقعة**

بعد تنفيذ الملف بنجاح، ستحصل على:

### 📊 **جداول جديدة:**
- ✅ `user_sessions` - لتتبع جلسات المستخدمين
- ✅ `user_activity_log` - لتسجيل جميع الأنشطة

### 🔄 **جدول محدث:**
- ✅ `users` - مع أعمدة جديدة لتتبع الحالة

### 🛠️ **دوال مساعدة:**
- ✅ `update_user_last_activity()` - لتحديث آخر نشاط
- ✅ `get_online_users()` - للحصول على المستخدمين المتصلين
- ✅ `end_user_session()` - لإنهاء الجلسة
- ✅ `cleanup_old_sessions()` - لتنظيف الجلسات القديمة

### 👁️ **Views مفيدة:**
- ✅ `v_online_users` - عرض المستخدمين المتصلين
- ✅ `v_daily_activity_stats` - إحصائيات النشاط اليومي

---

## 🚨 **استكشاف الأخطاء**

### ❌ **خطأ: "relation does not exist"**
```
ERROR: relation "users" does not exist
```
**الحل:** تأكد من وجود جدول `users` أولاً

### ❌ **خطأ: "permission denied"**
```
ERROR: permission denied for table users
```
**الحل:** تأكد من استخدام Service Role Key في Supabase

### ❌ **خطأ: "column already exists"**
```
ERROR: column "last_seen" of relation "users" already exists
```
**الحل:** هذا طبيعي، الكود يستخدم `IF NOT EXISTS`

---

## 📱 **الخطوة 4: الخطوات التالية**

بعد إنشاء الجداول بنجاح:

1. **🔧 تحديث تطبيق الكاميرا** لتسجيل الجلسات والأنشطة
2. **🖥️ تحديث تطبيق الإدارة** لعرض المستخدمين المتصلين
3. **⚡ إعداد التحديثات المباشرة** (Real-time)
4. **🧪 اختبار النظام** بالكامل

---

## 📞 **الدعم**

إذا واجهت أي مشاكل:
1. تحقق من رسائل الخطأ في SQL Editor
2. تأكد من صحة اتصال قاعدة البيانات
3. راجع صلاحيات المستخدم في Supabase

---

## 🎉 **تهانينا!**

إذا تم تنفيذ كل شيء بنجاح، فأنت الآن جاهز للانتقال إلى **المرحلة 2: تحديث تطبيق الكاميرا**! 🚀
