import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/device_model.dart';
import 'package:flutter/foundation.dart';

class DevicesService {
  static final DevicesService _instance = DevicesService._internal();
  factory DevicesService() => _instance;
  DevicesService._internal();

  static DevicesService get instance => _instance;

  final SupabaseClient _supabase = Supabase.instance.client;

  /// جلب جميع الأجهزة مع الفلترة والبحث
  Future<Map<String, dynamic>> getDevices({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    String? type,
    String? userId,
    String? locationId,
  }) async {
    try {
      debugPrint('🔍 جلب الأجهزة - الصفحة: $page، الحد: $limit');

      var query = _supabase
          .from('devices')
          .select('*');

      // تطبيق الفلاتر
      if (search?.isNotEmpty == true) {
        query = query.or('device_name.ilike.%$search%,device_id.ilike.%$search%,model.ilike.%$search%');
      }

      if (status?.isNotEmpty == true) {
        query = query.eq('status', status!);
      }

      if (type?.isNotEmpty == true) {
        query = query.eq('device_type', type!);
      }

      if (userId?.isNotEmpty == true) {
        query = query.eq('user_id', userId!);
      }

      if (locationId?.isNotEmpty == true) {
        query = query.eq('location_id', locationId!);
      }

      // ترتيب وتقسيم الصفحات
      final countQuery = await query.count(CountOption.exact);
      final totalCount = countQuery.count;

      final response = await query
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      final devices = (response as List)
          .map((json) => DeviceModel.fromJson(json))
          .toList();

      debugPrint('✅ تم جلب ${devices.length} جهاز من أصل $totalCount');

      return {
        'success': true,
        'devices': devices,
        'totalCount': totalCount,
        'currentPage': page,
        'totalPages': (totalCount / limit).ceil(),
        'hasMore': page * limit < totalCount,
      };
    } catch (error) {
      debugPrint('❌ خطأ في جلب الأجهزة: $error');
      return {
        'success': false,
        'error': 'فشل في جلب الأجهزة: ${error.toString()}',
        'devices': <DeviceModel>[],
        'totalCount': 0,
      };
    }
  }

  /// إنشاء جهاز جديد
  Future<Map<String, dynamic>> createDevice({
    required String deviceId,
    required String deviceName,
    required String deviceType,
    required String model,
    required String userId,
    String? locationId,
    String? description,
    Map<String, dynamic>? specifications,
  }) async {
    try {
      debugPrint('🔧 إنشاء جهاز جديد: $deviceName');

      // التحقق من عدم وجود جهاز بنفس المعرف
      final existingDevice = await _supabase
          .from('devices')
          .select('id')
          .eq('device_id', deviceId)
          .maybeSingle();

      if (existingDevice != null) {
        return {
          'success': false,
          'error': 'يوجد جهاز بهذا المعرف مسبقاً: $deviceId',
        };
      }

      final deviceData = {
        'device_id': deviceId,
        'device_name': deviceName,
        'device_type': deviceType,
        'model': model,
        'user_id': userId,
        'location_id': locationId,
        'description': description,
        'specifications': specifications,
        'status': 'active',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('devices')
          .insert(deviceData)
          .select('''
            *,
            users!devices_user_id_fkey(id, full_name, national_id),
            locations!devices_location_id_fkey(id, name, address)
          ''')
          .single();

      final device = DeviceModel.fromJson(response);

      debugPrint('✅ تم إنشاء الجهاز بنجاح: ${device.deviceName}');

      return {
        'success': true,
        'device': device,
        'message': 'تم إنشاء الجهاز بنجاح',
      };
    } catch (error) {
      debugPrint('❌ خطأ في إنشاء الجهاز: $error');
      return {
        'success': false,
        'error': 'فشل في إنشاء الجهاز: ${error.toString()}',
      };
    }
  }

  /// تحديث جهاز
  Future<Map<String, dynamic>> updateDevice({
    required String deviceId,
    String? deviceName,
    String? deviceType,
    String? model,
    String? userId,
    String? locationId,
    String? description,
    String? status,
    Map<String, dynamic>? specifications,
  }) async {
    try {
      debugPrint('🔄 تحديث الجهاز: $deviceId');

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (deviceName != null) updateData['device_name'] = deviceName;
      if (deviceType != null) updateData['device_type'] = deviceType;
      if (model != null) updateData['model'] = model;
      if (userId != null) updateData['user_id'] = userId;
      if (locationId != null) updateData['location_id'] = locationId;
      if (description != null) updateData['description'] = description;
      if (status != null) updateData['status'] = status;
      if (specifications != null) updateData['specifications'] = specifications;

      final response = await _supabase
          .from('devices')
          .update(updateData)
          .eq('id', deviceId)
          .select('''
            *,
            users!devices_user_id_fkey(id, full_name, national_id),
            locations!devices_location_id_fkey(id, name, address)
          ''')
          .single();

      final device = DeviceModel.fromJson(response);

      debugPrint('✅ تم تحديث الجهاز بنجاح: ${device.deviceName}');

      return {
        'success': true,
        'device': device,
        'message': 'تم تحديث الجهاز بنجاح',
      };
    } catch (error) {
      debugPrint('❌ خطأ في تحديث الجهاز: $error');
      return {
        'success': false,
        'error': 'فشل في تحديث الجهاز: ${error.toString()}',
      };
    }
  }

  /// حذف جهاز
  Future<Map<String, dynamic>> deleteDevice({
    required String deviceId,
  }) async {
    try {
      debugPrint('🗑️ حذف الجهاز: $deviceId');

      // حذف الصور والفيديوهات المرتبطة بالجهاز
      await _supabase.from('photos').delete().eq('device_id', deviceId);
      await _supabase.from('videos').delete().eq('device_id', deviceId);

      // حذف الجهاز
      await _supabase.from('devices').delete().eq('id', deviceId);

      debugPrint('✅ تم حذف الجهاز بنجاح');

      return {
        'success': true,
        'message': 'تم حذف الجهاز بنجاح',
      };
    } catch (error) {
      debugPrint('❌ خطأ في حذف الجهاز: $error');
      return {
        'success': false,
        'error': 'فشل في حذف الجهاز: ${error.toString()}',
      };
    }
  }

  /// تغيير حالة الجهاز
  Future<Map<String, dynamic>> toggleDeviceStatus({
    required String deviceId,
  }) async {
    try {
      debugPrint('🔄 تغيير حالة الجهاز: $deviceId');

      // جلب الحالة الحالية
      final currentDevice = await _supabase
          .from('devices')
          .select('status')
          .eq('id', deviceId)
          .single();

      final currentStatus = currentDevice['status'] as String;
      final newStatus = currentStatus == 'active' ? 'inactive' : 'active';

      final response = await _supabase
          .from('devices')
          .update({
            'status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', deviceId)
          .select('''
            *,
            users!devices_user_id_fkey(id, full_name, national_id),
            locations!devices_location_id_fkey(id, name, address)
          ''')
          .single();

      final device = DeviceModel.fromJson(response);

      debugPrint('✅ تم تغيير حالة الجهاز إلى: $newStatus');

      return {
        'success': true,
        'device': device,
        'message': newStatus == 'active' ? 'تم تفعيل الجهاز' : 'تم إيقاف الجهاز',
      };
    } catch (error) {
      debugPrint('❌ خطأ في تغيير حالة الجهاز: $error');
      return {
        'success': false,
        'error': 'فشل في تغيير حالة الجهاز: ${error.toString()}',
      };
    }
  }

  /// جلب إحصائيات الأجهزة
  Future<Map<String, dynamic>> getDevicesStats() async {
    try {
      debugPrint('📊 جلب إحصائيات الأجهزة');

      final totalDevices = await _supabase
          .from('devices')
          .select('id')
          .count(CountOption.exact);

      final activeDevices = await _supabase
          .from('devices')
          .select('id')
          .eq('status', 'active')
          .count(CountOption.exact);

      final inactiveDevices = await _supabase
          .from('devices')
          .select('id')
          .eq('status', 'inactive')
          .count(CountOption.exact);

      final devicesByType = await _supabase
          .from('devices')
          .select('device_type')
          .count(CountOption.exact);

      debugPrint('✅ تم جلب إحصائيات الأجهزة');

      return {
        'success': true,
        'totalDevices': totalDevices.count,
        'activeDevices': activeDevices.count,
        'inactiveDevices': inactiveDevices.count,
        'devicesByType': devicesByType.count,
      };
    } catch (error) {
      debugPrint('❌ خطأ في جلب إحصائيات الأجهزة: $error');
      return {
        'success': false,
        'error': 'فشل في جلب إحصائيات الأجهزة: ${error.toString()}',
        'totalDevices': 0,
        'activeDevices': 0,
        'inactiveDevices': 0,
        'devicesByType': 0,
      };
    }
  }
}
