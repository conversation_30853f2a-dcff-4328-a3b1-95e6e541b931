-- 🔍 اختبار تشخيص نظام المستخدمين المتصلين
-- نفذ هذا الكود في Supabase SQL Editor لتشخيص المشكلة

-- ===================================
-- 1. فحص جدول المستخدمين الأساسي
-- ===================================

SELECT '🔍 فحص جدول المستخدمين الأساسي' as test_name;

SELECT 
    full_name,
    email,
    is_active,
    is_online,
    last_seen,
    EXTRACT(EPOCH FROM (NOW() - last_seen))/60 as minutes_since_last_seen,
    CASE
        WHEN last_seen > NOW() - INTERVAL '2 minutes' THEN '🟢 متصل الآن'
        WHEN last_seen > NOW() - INTERVAL '5 minutes' THEN '🟡 نشط مؤخراً'
        WHEN last_seen > NOW() - INTERVAL '30 minutes' THEN '🔶 غير نشط'
        ELSE '🔴 غير متصل'
    END as status_text
FROM users 
WHERE is_active = true
ORDER BY last_seen DESC;

-- ===================================
-- 2. فحص جدول الجلسات
-- ===================================

SELECT '🔍 فحص جدول user_sessions' as test_name;

-- التحقق من وجود الجدول
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_sessions') 
        THEN '✅ جدول user_sessions موجود'
        ELSE '❌ جدول user_sessions غير موجود'
    END as table_status;

-- إذا كان الجدول موجود، اعرض محتوياته
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_sessions') THEN
        RAISE NOTICE '📊 محتويات جدول user_sessions:';
    END IF;
END $$;

-- أولاً، دعنا نرى ما هي الأعمدة الموجودة في جدول user_sessions
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'user_sessions'
ORDER BY ordinal_position;

-- عرض الجلسات النشطة (إذا كان الجدول موجود)
SELECT
    u.full_name,
    u.email,
    s.*
FROM users u
JOIN user_sessions s ON u.id = s.user_id
WHERE s.is_active = true
ORDER BY s.last_activity DESC;

-- ===================================
-- 3. فحص View المستخدمين المتصلين
-- ===================================

SELECT '🔍 فحص v_simple_online_users' as test_name;

-- التحقق من وجود الـ View
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_simple_online_users') 
        THEN '✅ View v_simple_online_users موجود'
        ELSE '❌ View v_simple_online_users غير موجود'
    END as view_status;

-- عرض محتويات الـ View (إذا كان موجود)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_simple_online_users') THEN
        RAISE NOTICE '📊 محتويات v_simple_online_users:';
    END IF;
END $$;

SELECT * FROM v_simple_online_users ORDER BY last_seen DESC;

-- ===================================
-- 4. فحص مستخدم anan تحديداً
-- ===================================

SELECT '🔍 فحص مستخدم anan تحديداً' as test_name;

SELECT 
    'بيانات المستخدم anan:' as info,
    full_name,
    email,
    is_active,
    is_online,
    last_seen,
    last_login,
    current_session_id,
    total_sessions,
    last_location_name,
    created_at,
    updated_at
FROM users 
WHERE email LIKE '%anan%' OR full_name LIKE '%anan%' OR email = '<EMAIL>';

-- ===================================
-- 5. فحص جلسات anan
-- ===================================

SELECT '🔍 فحص جلسات anan' as test_name;

-- أولاً، دعنا نتحقق من وجود جلسات لـ anan
SELECT
    'عدد جلسات anan:' as info,
    COUNT(*) as session_count
FROM users u
LEFT JOIN user_sessions s ON u.id = s.user_id
WHERE u.email LIKE '%anan%' OR u.full_name LIKE '%anan%' OR u.email = '<EMAIL>';

-- ثم عرض تفاصيل الجلسات
SELECT
    'جلسات المستخدم anan:' as info,
    s.*
FROM users u
LEFT JOIN user_sessions s ON u.id = s.user_id
WHERE u.email LIKE '%anan%' OR u.full_name LIKE '%anan%' OR u.email = '<EMAIL>'
ORDER BY s.last_activity DESC;

-- ===================================
-- 6. فحص آخر الأنشطة
-- ===================================

SELECT '🔍 فحص آخر الأنشطة' as test_name;

-- التحقق من وجود جدول الأنشطة
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_activity_log') 
        THEN '✅ جدول user_activity_log موجود'
        ELSE '❌ جدول user_activity_log غير موجود'
    END as activity_table_status;

-- أولاً، دعنا نرى ما هي الأعمدة الموجودة في جدول user_activity_log
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'user_activity_log'
ORDER BY ordinal_position;

-- عرض آخر الأنشطة (إذا كان الجدول موجود)
SELECT
    u.full_name,
    u.email,
    a.*
FROM users u
JOIN user_activity_log a ON u.id = a.user_id
WHERE a.timestamp > NOW() - INTERVAL '1 hour'
ORDER BY a.timestamp DESC
LIMIT 10;

-- ===================================
-- 7. اختبار تحديث حالة anan يدوياً
-- ===================================

SELECT '🔧 اختبار تحديث حالة anan يدوياً' as test_name;

-- تحديث آخر ظهور لـ anan إلى الآن
UPDATE users 
SET 
    last_seen = NOW(),
    is_online = true,
    updated_at = NOW()
WHERE email = '<EMAIL>' OR email LIKE '%anan%';

-- التحقق من التحديث
SELECT 
    'بعد التحديث:' as status,
    full_name,
    email,
    is_online,
    last_seen,
    EXTRACT(EPOCH FROM (NOW() - last_seen))/60 as minutes_since_last_seen
FROM users 
WHERE email = '<EMAIL>' OR email LIKE '%anan%';

-- ===================================
-- 8. إحصائيات نهائية
-- ===================================

SELECT '📊 إحصائيات نهائية' as test_name;

SELECT 
    'إجمالي المستخدمين' as metric, 
    COUNT(*) as count 
FROM users
UNION ALL
SELECT 
    'المستخدمين النشطين', 
    COUNT(*) 
FROM users 
WHERE is_active = true
UNION ALL
SELECT 
    'المستخدمين المتصلين (حسب is_online)', 
    COUNT(*) 
FROM users 
WHERE is_online = true
UNION ALL
SELECT 
    'المستخدمين المتصلين (آخر دقيقتين)', 
    COUNT(*) 
FROM users 
WHERE last_seen > NOW() - INTERVAL '2 minutes' AND is_active = true
UNION ALL
SELECT 
    'المستخدمين النشطين (آخر 5 دقائق)', 
    COUNT(*) 
FROM users 
WHERE last_seen > NOW() - INTERVAL '5 minutes' AND is_active = true;

-- ===================================
-- 9. توصيات الإصلاح
-- ===================================

SELECT '💡 توصيات الإصلاح' as recommendations;

SELECT 
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_sessions')
        THEN '❌ يجب إنشاء جدول user_sessions'
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_simple_online_users')
        THEN '❌ يجب إنشاء view v_simple_online_users'
        WHEN (SELECT COUNT(*) FROM user_sessions WHERE is_active = true) = 0
        THEN '❌ لا توجد جلسات نشطة - تطبيق الكاميرا لا يسجل الجلسات'
        ELSE '✅ البنية الأساسية موجودة'
    END as diagnosis;

SELECT '🎯 الخطوات التالية:' as next_steps;
SELECT '1. تأكد من أن تطبيق الكاميرا يستخدم SessionService' as step_1;
SELECT '2. تأكد من استدعاء startSession عند تسجيل الدخول' as step_2;
SELECT '3. تأكد من استدعاء updateActivity كل دقيقة' as step_3;
SELECT '4. تحقق من أن البيانات تصل إلى قاعدة البيانات' as step_4;
