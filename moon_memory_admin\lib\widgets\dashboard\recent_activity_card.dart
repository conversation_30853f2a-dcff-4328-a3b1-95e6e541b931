import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_colors.dart';
import '../../models/user_model.dart';
import '../../models/photo_model.dart';
import '../../models/video_model.dart';

class RecentActivityCard extends StatelessWidget {
  final List<UserModel> recentUsers;
  final List<PhotoModel> recentPhotos;
  final List<VideoModel> recentVideos;

  const RecentActivityCard({
    super.key,
    required this.recentUsers,
    required this.recentPhotos,
    required this.recentVideos,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: AppColors.primaryGold,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'النشاطات الحديثة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.primaryGold,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // التبويبات
            DefaultTabController(
              length: 3,
              child: Column(
                children: [
                  TabBar(
                    labelColor: AppColors.primaryGold,
                    unselectedLabelColor: AppColors.secondaryText,
                    indicatorColor: AppColors.primaryGold,
                    tabs: const [
                      Tab(text: 'المستخدمين الجدد'),
                      Tab(text: 'الصور الحديثة'),
                      Tab(text: 'الفيديوهات الحديثة'),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  SizedBox(
                    height: 300,
                    child: TabBarView(
                      children: [
                        _buildRecentUsers(context),
                        _buildRecentPhotos(context),
                        _buildRecentVideos(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentUsers(BuildContext context) {
    if (recentUsers.isEmpty) {
      return _buildEmptyState(context, 'لا توجد مستخدمين جدد', Icons.person_off);
    }

    return ListView.builder(
      itemCount: recentUsers.length,
      itemBuilder: (context, index) {
        final user = recentUsers[index];
        return _buildUserItem(context, user);
      },
    );
  }

  Widget _buildRecentPhotos(BuildContext context) {
    if (recentPhotos.isEmpty) {
      return _buildEmptyState(context, 'لا توجد صور حديثة', Icons.photo_library);
    }

    return ListView.builder(
      itemCount: recentPhotos.length,
      itemBuilder: (context, index) {
        final photo = recentPhotos[index];
        return _buildPhotoItem(context, photo);
      },
    );
  }

  Widget _buildRecentVideos(BuildContext context) {
    if (recentVideos.isEmpty) {
      return _buildEmptyState(context, 'لا توجد فيديوهات حديثة', Icons.video_library);
    }

    return ListView.builder(
      itemCount: recentVideos.length,
      itemBuilder: (context, index) {
        final video = recentVideos[index];
        return _buildVideoItem(context, video);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: AppColors.mutedText,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.mutedText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserItem(BuildContext context, UserModel user) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: AppColors.primaryGold,
            child: Text(
              user.fullName?.substring(0, 1) ?? 'م',
              style: const TextStyle(
                color: AppColors.darkBackground,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.fullName ?? 'مستخدم غير محدد',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppColors.primaryText,
                  ),
                ),
                Text(
                  user.department ?? 'قسم غير محدد',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          Text(
            _formatDate(user.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.mutedText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoItem(BuildContext context, PhotoModel photo) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.photo,
              color: AppColors.success,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  photo.fileName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppColors.primaryText,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '${photo.fullLocationCode} - ${photo.username}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          Text(
            _formatDate(photo.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.mutedText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoItem(BuildContext context, VideoModel video) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.videocam,
              color: AppColors.warning,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  video.fileName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppColors.primaryText,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '${video.fullLocationCode ?? video.location} - ${video.username}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          Text(
            _formatDate(video.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.mutedText,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'غير محدد';
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }
}
