import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_colors.dart';
import '../providers/locations_provider.dart';
import '../providers/active_users_provider.dart';
import '../models/location_model.dart';
import '../models/active_user_model.dart';
import '../widgets/locations/interactive_map_widget.dart';
import '../widgets/locations/active_users_panel.dart';

class LocationsScreen extends ConsumerStatefulWidget {
  const LocationsScreen({super.key});

  @override
  ConsumerState<LocationsScreen> createState() => _LocationsScreenState();
}

class _LocationsScreenState extends ConsumerState<LocationsScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل المواقع وبدء خدمة المستخدمين عند بدء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(locationsProvider.notifier).loadLocations();
      // بدء خدمة المستخدمين النشطين
      ref.read(activeUsersProvider.notifier);
    });
  }

  // دالة للانتقال إلى موقع المستخدم
  void _navigateToUserLocation(ActiveUserModel user) {
    // تحديد المستخدم المحدد
    ref.read(activeUsersProvider.notifier).selectUser(user);

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🎯 تم الانتقال إلى موقع ${user.name}'),
        duration: const Duration(seconds: 1),
        backgroundColor: AppColors.primaryGold,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final locationsState = ref.watch(locationsProvider);
    final selectedLocation = ref.watch(selectedLocationProvider);

    return Scaffold(
      backgroundColor: AppColors.surfaceBackground,
      body: Column(
        children: [
          // شريط الإحصائيات
          _buildStatsBar(locationsState),

          // المحتوى الرئيسي
          Expanded(
            child: Row(
              children: [
                // الخريطة التفاعلية
                Expanded(
                  flex: 3,
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.cardBackground,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: locationsState.isLoading
                          ? _buildLoadingView()
                          : locationsState.error != null
                              ? _buildErrorView(locationsState.error!)
                              : _buildMapView(locationsState.locations),
                    ),
                  ),
                ),

                // لوحة المستخدمين النشطين
                ActiveUsersPanel(
                  onUserLocationTap: _navigateToUserLocation,
                ),

                // لوحة تفاصيل الموقع المحدد
                if (selectedLocation != null)
                  _buildLocationDetailsPanel(selectedLocation),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // شريط الإحصائيات
  Widget _buildStatsBar(LocationsState locationsState) {
    final stats = locationsState.stats;
    final globalStats = ref.watch(globalStatsProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildStatCard(
            'إجمالي المواقع',
            '${stats['totalLocations'] ?? 0}',
            Icons.location_on,
            AppColors.info,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'المستخدمون النشطون',
            '${globalStats['activeUsers'] ?? 0}',
            Icons.people,
            AppColors.success,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'المتصلون الآن',
            '${globalStats['onlineUsers'] ?? 0}',
            Icons.wifi,
            AppColors.primaryGold,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'الوسائط اليوم',
            '${globalStats['totalMediaToday'] ?? 0}',
            Icons.photo_library,
            AppColors.warning,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            'البلدان',
            '${globalStats['countries'] ?? 0}',
            Icons.public,
            AppColors.info,
          ),
        ],
      ),
    );
  }

  // بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // عرض الخريطة
  Widget _buildMapView(List<LocationModel> locations) {
    return Container(
      color: AppColors.surfaceBackground,
      child: locations.isEmpty
          ? _buildEmptyView()
          : Column(
              children: [
                // عنوان الخريطة
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primaryGold.withValues(alpha: 0.1),
                    border: Border(
                      bottom: BorderSide(
                        color: AppColors.primaryGold.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.map,
                        color: AppColors.primaryGold,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'خريطة المواقع والأجهزة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryText,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${locations.length} موقع',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                ),

                // الخريطة التفاعلية
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: InteractiveMapWidget(
                      locations: locations,
                    ),
                  ),
                ),
              ],
            ),
    );
  }





  // لوحة تفاصيل الموقع المحدد
  Widget _buildLocationDetailsPanel(LocationModel location) {
    return Container(
      width: 350,
      margin: const EdgeInsets.only(top: 16, right: 16, bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان اللوحة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primaryGold.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getLocationIcon(location),
                  color: AppColors.primaryGold,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'تفاصيل الموقع',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryText,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => ref.read(locationsProvider.notifier).clearSelection(),
                  icon: const Icon(Icons.close),
                  color: AppColors.secondaryText,
                ),
              ],
            ),
          ),

          // محتوى اللوحة
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات أساسية
                  _buildDetailRow('اسم الموقع', location.displayName),
                  _buildDetailRow('رمز الموقع', location.locationCode),
                  _buildDetailRow('نوع الموقع', location.locationTypeArabic),
                  if (location.address != null)
                    _buildDetailRow('العنوان', location.address!),

                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),

                  // إحصائيات
                  Text(
                    'الإحصائيات',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryText,
                    ),
                  ),
                  const SizedBox(height: 12),

                  _buildStatRow('عدد الأجهزة', '${location.devicesCount ?? 0}', Icons.devices),
                  _buildStatRow('عدد الصور', '${location.totalPhotos}', Icons.photo),
                  _buildStatRow('عدد الفيديوهات', '${location.totalVideos}', Icons.videocam),
                  _buildStatRow('إجمالي الوسائط', '${location.totalMedia}', Icons.photo_library),

                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),

                  // أجهزة الموقع
                  Text(
                    'الأجهزة في هذا الموقع',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryText,
                    ),
                  ),
                  const SizedBox(height: 12),

                  Expanded(
                    child: Consumer(
                      builder: (context, ref, child) {
                        final devices = ref.watch(selectedLocationDevicesProvider);

                        if (devices.isEmpty) {
                          return Center(
                            child: Text(
                              'لا توجد أجهزة في هذا الموقع',
                              style: TextStyle(
                                color: AppColors.secondaryText,
                                fontSize: 14,
                              ),
                            ),
                          );
                        }

                        return ListView.builder(
                          itemCount: devices.length,
                          itemBuilder: (context, index) {
                            final device = devices[index];
                            return _buildDeviceItem(device);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // صف تفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.primaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // صف إحصائية
  Widget _buildStatRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: AppColors.primaryGold),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
        ],
      ),
    );
  }

  // عنصر جهاز
  Widget _buildDeviceItem(dynamic device) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          // أيقونة الجهاز
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primaryGold.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.devices,
              color: AppColors.primaryGold,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // معلومات الجهاز
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.deviceName ?? 'جهاز غير محدد',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primaryText,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  device.deviceType ?? 'نوع غير محدد',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),

          // حالة الجهاز
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getDeviceStatusColor(device.status),
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  // عرض فارغ
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64,
            color: AppColors.secondaryText,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مواقع',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أي مواقع مسجلة',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  // عرض التحميل
  Widget _buildLoadingView() {
    return Container(
      color: AppColors.surfaceBackground,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppColors.primaryGold,
            ),
            SizedBox(height: 16),
            Text(
              'جاري تحميل المواقع...',
              style: TextStyle(
                color: AppColors.secondaryText,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عرض الخطأ
  Widget _buildErrorView(String error) {
    return Container(
      color: AppColors.surfaceBackground,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل المواقع',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => ref.read(locationsProvider.notifier).refresh(),
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGold,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }



  // الحصول على أيقونة الموقع
  IconData _getLocationIcon(LocationModel location) {
    if (location.isULocation) {
      return Icons.account_balance;
    } else {
      return Icons.school;
    }
  }

  // الحصول على لون حالة الجهاز
  Color _getDeviceStatusColor(String? status) {
    switch (status) {
      case 'active':
        return AppColors.success;
      case 'inactive':
        return AppColors.error;
      case 'maintenance':
        return AppColors.warning;
      default:
        return AppColors.secondaryText;
    }
  }
}
