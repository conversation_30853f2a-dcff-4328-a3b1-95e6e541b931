import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../providers/active_users_provider.dart';
import '../models/active_user_model.dart';
import '../core/theme/app_colors.dart';

class OnlineUsersScreen extends ConsumerStatefulWidget {
  const OnlineUsersScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<OnlineUsersScreen> createState() => _OnlineUsersScreenState();
}

class _OnlineUsersScreenState extends ConsumerState<OnlineUsersScreen> {
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      ref.read(activeUsersProvider.notifier).refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(activeUsersProvider);
    final globalStats = ref.watch(globalStatsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('المستخدمين المتصلين الآن'),
        backgroundColor: AppColors.primaryGold,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(activeUsersProvider.notifier).refresh(),
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: Icon(state.showOnlineOnly ? Icons.visibility : Icons.visibility_off),
            onPressed: () => ref.read(activeUsersProvider.notifier).toggleOnlineOnly(),
            tooltip: state.showOnlineOnly ? 'عرض الجميع' : 'المتصلين فقط',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات
          _buildStatsBar(globalStats),
          
          // شريط البحث والفلاتر
          _buildSearchAndFilters(state),
          
          // قائمة المستخدمين
          Expanded(
            child: _buildUsersList(state),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsBar(Map<String, dynamic> stats) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.primaryGold.withOpacity(0.1),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'المتصلين الآن',
            '${stats['onlineUsers'] ?? 0}',
            Icons.circle,
            Colors.green,
          ),
          _buildStatItem(
            'النشطين',
            '${stats['activeUsers'] ?? 0}',
            Icons.trending_up,
            Colors.orange,
          ),
          _buildStatItem(
            'الإجمالي',
            '${stats['totalUsers'] ?? 0}',
            Icons.people,
            AppColors.primaryGold,
          ),
          _buildStatItem(
            'البلدان',
            '${stats['countries'] ?? 0}',
            Icons.public,
            Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters(ActiveUsersState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: InputDecoration(
              hintText: 'البحث عن مستخدم...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            onChanged: (value) {
              ref.read(activeUsersProvider.notifier).updateSearch(value);
            },
          ),
          
          const SizedBox(height: 12),
          
          // فلتر البلدان
          if (state.availableCountries.isNotEmpty)
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'فلترة حسب البلد',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              value: state.selectedCountry.isEmpty ? null : state.selectedCountry,
              items: [
                const DropdownMenuItem<String>(
                  value: '',
                  child: Text('جميع البلدان'),
                ),
                ...state.availableCountries.map((country) {
                  return DropdownMenuItem<String>(
                    value: country,
                    child: Text(country),
                  );
                }),
              ],
              onChanged: (value) {
                ref.read(activeUsersProvider.notifier).updateSelectedCountry(value ?? '');
              },
            ),
        ],
      ),
    );
  }

  Widget _buildUsersList(ActiveUsersState state) {
    if (state.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل المستخدمين...'),
          ],
        ),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('خطأ: ${state.error}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.read(activeUsersProvider.notifier).refresh(),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    final filteredUsers = state.filteredUsers;

    if (filteredUsers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              state.showOnlineOnly ? Icons.wifi_off : Icons.people_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              state.showOnlineOnly 
                  ? 'لا يوجد مستخدمين متصلين حالياً'
                  : 'لا يوجد مستخدمين',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            if (state.searchQuery.isNotEmpty || state.selectedCountry.isNotEmpty) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: () => ref.read(activeUsersProvider.notifier).clearFilters(),
                child: const Text('مسح الفلاتر'),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.read(activeUsersProvider.notifier).refresh();
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredUsers.length,
        itemBuilder: (context, index) {
          final user = filteredUsers[index];
          return _buildUserCard(user);
        },
      ),
    );
  }

  Widget _buildUserCard(ActiveUserModel user) {
    final isOnline = user.isOnline;
    final statusColor = _getStatusColor(user.status);
    final statusText = _getStatusText(user.status);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: statusColor,
              child: Text(
                user.name.isNotEmpty ? user.name[0].toUpperCase() : '؟',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: isOnline ? Colors.green : Colors.grey,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
              ),
            ),
          ],
        ),
        title: Text(
          user.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('📧 ${user.email}'),
            Text('📍 ${user.address}'),
            if (user.deviceModel.isNotEmpty)
              Text('📱 ${user.deviceModel}'),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    statusText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _formatLastSeen(user.lastSeen),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '📸 ${user.stats.photosToday}',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '🎥 ${user.stats.videosToday}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        onTap: () => _showUserDetails(user),
      ),
    );
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.working:
        return Colors.green;
      case UserStatus.available:
        return Colors.orange;
      case UserStatus.break_:
        return Colors.blue;
      case UserStatus.photography:
        return Colors.purple;
      case UserStatus.meeting:
        return Colors.red;
      case UserStatus.offline:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.working:
        return 'متصل الآن';
      case UserStatus.available:
        return 'نشط مؤخراً';
      case UserStatus.break_:
        return 'غير نشط';
      case UserStatus.photography:
        return 'يصور';
      case UserStatus.meeting:
        return 'في اجتماع';
      case UserStatus.offline:
        return 'غير متصل';
      default:
        return 'غير محدد';
    }
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} د';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} س';
    } else {
      return '${difference.inDays} ي';
    }
  }

  void _showUserDetails(ActiveUserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(user.name),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('البريد الإلكتروني', user.email),
              _buildDetailRow('الموقع', user.address),
              _buildDetailRow('المدينة', user.city),
              _buildDetailRow('البلد', user.country),
              _buildDetailRow('نوع الجهاز', user.deviceType),
              _buildDetailRow('موديل الجهاز', user.deviceModel),
              _buildDetailRow('آخر ظهور', _formatLastSeen(user.lastSeen)),
              _buildDetailRow('الحالة', _getStatusText(user.status)),
              const Divider(),
              const Text('الإحصائيات:', style: TextStyle(fontWeight: FontWeight.bold)),
              _buildDetailRow('صور اليوم', '${user.stats.photosToday}'),
              _buildDetailRow('فيديوهات اليوم', '${user.stats.videosToday}'),
              _buildDetailRow('إجمالي الصور', '${user.stats.totalPhotos}'),
              _buildDetailRow('إجمالي الفيديوهات', '${user.stats.totalVideos}'),
              _buildDetailRow('تاريخ الانضمام', user.stats.joinDate.toString().split(' ')[0]),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    if (value.isEmpty) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
