import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_colors.dart';
import '../providers/media_provider.dart';
import '../widgets/media/media_stats_bar.dart';
import '../widgets/media/media_filter_bar.dart';
import '../widgets/media/media_grid_view.dart';

class MediaGalleryScreen extends ConsumerWidget {
  const MediaGalleryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaState = ref.watch(mediaProvider);

    return Scaffold(
      backgroundColor: AppColors.surfaceBackground,
      body: Column(
        children: [
          // أزرار الإجراءات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            child: Row(
              children: [
                const Spacer(),
                OutlinedButton.icon(
                  onPressed: () {
                    ref.read(mediaProvider.notifier).refresh();
                  },
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('تحديث المعرض', style: TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: Size.zero,
                  ),
                ),
              ],
            ),
          ),

          // شريط الإحصائيات
          const MediaStatsBar(),

          // شريط الفلترة والبحث
          const MediaFilterBar(),

          // معرض الوسائط
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(24),
              child: Card(
                elevation: 1,
                color: AppColors.cardBackground,
                child: Column(
                  children: [
                    // رأس المعرض
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.surfaceBackground,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(
                            'معرض الوسائط',
                            style: const TextStyle(
                              color: AppColors.primaryGold,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          if (mediaState.isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: AppColors.primaryGold,
                              ),
                            ),
                          const SizedBox(width: 16),
                          Text(
                            '${mediaState.totalMedia} عنصر',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.secondaryText,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // محتوى المعرض
                    Expanded(
                      child: mediaState.error != null
                          ? _buildErrorState(context, mediaState.error!, ref)
                          : (mediaState.photos.isEmpty && mediaState.videos.isEmpty && !mediaState.isLoading)
                              ? _buildEmptyState(context)
                              : const MediaGridView(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل الوسائط',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(mediaProvider.notifier).refresh();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryGold,
              foregroundColor: AppColors.darkBackground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library,
            size: 64,
            color: AppColors.secondaryText,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد وسائط',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أي صور أو فيديوهات مطابقة للفلاتر المحددة',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: () => _showUploadDialog(context, 'photo'),
                icon: const Icon(Icons.add_photo_alternate),
                label: const Text('رفع صور'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryGold,
                  foregroundColor: AppColors.darkBackground,
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: () => _showUploadDialog(context, 'video'),
                icon: const Icon(Icons.video_call),
                label: const Text('رفع فيديوهات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.info,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showUploadDialog(BuildContext context, String type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('رفع ${type == 'photo' ? 'صور' : 'فيديوهات'}'),
        content: Text('سيتم تطوير ميزة رفع ${type == 'photo' ? 'الصور' : 'الفيديوهات'} قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
