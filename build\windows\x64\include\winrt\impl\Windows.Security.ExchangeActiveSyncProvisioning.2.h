// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Security_ExchangeActiveSyncProvisioning_2_H
#define WINRT_Windows_Security_ExchangeActiveSyncProvisioning_2_H
#include "winrt/impl/Windows.Security.ExchangeActiveSyncProvisioning.1.h"
WINRT_EXPORT namespace winrt::Windows::Security::ExchangeActiveSyncProvisioning
{
    struct __declspec(empty_bases) EasClientDeviceInformation : winrt::Windows::Security::ExchangeActiveSyncProvisioning::IEasClientDeviceInformation,
        impl::require<EasClientDeviceInformation, winrt::Windows::Security::ExchangeActiveSyncProvisioning::IEasClientDeviceInformation2>
    {
        EasClientDeviceInformation(std::nullptr_t) noexcept {}
        EasClientDeviceInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Security::ExchangeActiveSyncProvisioning::IEasClientDeviceInformation(ptr, take_ownership_from_abi) {}
        EasClientDeviceInformation();
    };
}
#endif
