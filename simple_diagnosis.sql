-- 🔍 تشخيص بسيط لمشكلة المستخدمين المتصلين

-- ===================================
-- 1. فحص المستخدمين الأساسي
-- ===================================

SELECT '🔍 جميع المستخدمين وحالتهم:' as test_name;

SELECT 
    full_name,
    email,
    is_active,
    is_online,
    last_seen,
    last_login,
    EXTRACT(EPOCH FROM (NOW() - last_seen))/60 as minutes_since_last_seen,
    CASE
        WHEN last_seen > NOW() - INTERVAL '2 minutes' THEN '🟢 متصل الآن'
        WHEN last_seen > NOW() - INTERVAL '5 minutes' THEN '🟡 نشط مؤخراً'
        WHEN last_seen > NOW() - INTERVAL '30 minutes' THEN '🔶 غير نشط'
        ELSE '🔴 غير متصل'
    END as status_text,
    created_at,
    updated_at
FROM users 
ORDER BY last_seen DESC NULLS LAST;

-- ===================================
-- 2. فحص مستخدم anan تحديداً
-- ===================================

SELECT '🔍 بيانات مستخدم anan:' as test_name;

SELECT *
FROM users 
WHERE email = '<EMAIL>' 
   OR email LIKE '%anan%' 
   OR full_name LIKE '%anan%';

-- ===================================
-- 3. فحص الجداول المتاحة
-- ===================================

SELECT '🔍 الجداول المتاحة:' as test_name;

SELECT 
    table_name,
    CASE 
        WHEN table_name = 'users' THEN '✅ أساسي'
        WHEN table_name = 'user_sessions' THEN '✅ جلسات'
        WHEN table_name = 'user_activity_log' THEN '✅ أنشطة'
        WHEN table_name = 'devices' THEN '✅ أجهزة'
        ELSE '📋 آخر'
    END as description
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('users', 'user_sessions', 'user_activity_log', 'devices')
ORDER BY table_name;

-- ===================================
-- 4. فحص الـ Views المتاحة
-- ===================================

SELECT '🔍 الـ Views المتاحة:' as test_name;

SELECT 
    table_name as view_name,
    CASE 
        WHEN table_name = 'v_simple_online_users' THEN '✅ مستخدمين متصلين'
        WHEN table_name = 'v_online_users' THEN '✅ مستخدمين متصلين تفصيلي'
        ELSE '📋 آخر'
    END as description
FROM information_schema.views 
WHERE table_schema = 'public' 
  AND table_name LIKE '%online%'
ORDER BY table_name;

-- ===================================
-- 5. اختبار v_simple_online_users إذا كان موجود
-- ===================================

SELECT '🔍 محتويات v_simple_online_users:' as test_name;

-- هذا سيعمل فقط إذا كان الـ view موجود
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_simple_online_users') THEN
        RAISE NOTICE 'View v_simple_online_users موجود';
    ELSE
        RAISE NOTICE 'View v_simple_online_users غير موجود';
    END IF;
END $$;

-- ===================================
-- 6. فحص جدول الأجهزة
-- ===================================

SELECT '🔍 الأجهزة المسجلة:' as test_name;

SELECT 
    d.device_name,
    d.device_model,
    d.device_brand,
    d.user_id,
    u.full_name,
    u.email,
    d.last_login,
    d.is_active,
    EXTRACT(EPOCH FROM (NOW() - d.last_login))/60 as minutes_since_device_login
FROM devices d
LEFT JOIN users u ON d.user_id = u.id
ORDER BY d.last_login DESC NULLS LAST;

-- ===================================
-- 7. تحديث حالة anan للاختبار
-- ===================================

SELECT '🔧 تحديث حالة anan للاختبار:' as test_name;

-- تحديث آخر ظهور لـ anan إلى الآن
UPDATE users 
SET 
    last_seen = NOW(),
    is_online = true,
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- التحقق من التحديث
SELECT 
    'بعد التحديث:' as status,
    full_name,
    email,
    is_online,
    last_seen,
    EXTRACT(EPOCH FROM (NOW() - last_seen))/60 as minutes_since_last_seen
FROM users 
WHERE email = '<EMAIL>';

-- ===================================
-- 8. إحصائيات سريعة
-- ===================================

SELECT '📊 إحصائيات سريعة:' as test_name;

SELECT 
    'إجمالي المستخدمين' as metric, 
    COUNT(*) as count 
FROM users
UNION ALL
SELECT 
    'المستخدمين النشطين', 
    COUNT(*) 
FROM users 
WHERE is_active = true
UNION ALL
SELECT 
    'المستخدمين المتصلين (is_online = true)', 
    COUNT(*) 
FROM users 
WHERE is_online = true
UNION ALL
SELECT 
    'المستخدمين المتصلين (آخر دقيقتين)', 
    COUNT(*) 
FROM users 
WHERE last_seen > NOW() - INTERVAL '2 minutes' AND is_active = true;

-- ===================================
-- 9. فحص آخر الأنشطة في الصور والفيديوهات
-- ===================================

SELECT '🔍 آخر الأنشطة في الصور:' as test_name;

SELECT 
    p.user_id,
    u.full_name,
    u.email,
    COUNT(*) as photos_count,
    MAX(p.upload_timestamp) as last_photo_upload,
    EXTRACT(EPOCH FROM (NOW() - MAX(p.upload_timestamp)))/60 as minutes_since_last_photo
FROM photos p
LEFT JOIN users u ON p.user_id = u.id
WHERE p.upload_timestamp > NOW() - INTERVAL '1 day'
GROUP BY p.user_id, u.full_name, u.email
ORDER BY last_photo_upload DESC;

SELECT '🔍 آخر الأنشطة في الفيديوهات:' as test_name;

SELECT 
    v.user_id,
    u.full_name,
    u.email,
    COUNT(*) as videos_count,
    MAX(v.upload_timestamp) as last_video_upload,
    EXTRACT(EPOCH FROM (NOW() - MAX(v.upload_timestamp)))/60 as minutes_since_last_video
FROM videos v
LEFT JOIN users u ON v.user_id = u.id
WHERE v.upload_timestamp > NOW() - INTERVAL '1 day'
GROUP BY v.user_id, u.full_name, u.email
ORDER BY last_video_upload DESC;

-- ===================================
-- 10. التشخيص النهائي
-- ===================================

SELECT '💡 التشخيص والتوصيات:' as diagnosis;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM users WHERE email = '<EMAIL>' AND last_seen > NOW() - INTERVAL '5 minutes') > 0
        THEN '✅ anan محدث في قاعدة البيانات'
        WHEN (SELECT COUNT(*) FROM users WHERE email = '<EMAIL>') = 0
        THEN '❌ مستخدم anan غير موجود'
        ELSE '⚠️ anan موجود لكن last_seen قديم - تطبيق الكاميرا لا يحدث البيانات'
    END as anan_status;

SELECT 
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_simple_online_users')
        THEN '❌ View v_simple_online_users غير موجود - يجب إنشاؤه'
        WHEN (SELECT COUNT(*) FROM devices WHERE last_login > NOW() - INTERVAL '5 minutes') = 0
        THEN '⚠️ لا توجد أجهزة نشطة - تطبيق الكاميرا لا يحدث بيانات الأجهزة'
        ELSE '✅ البنية الأساسية تبدو سليمة'
    END as system_status;
