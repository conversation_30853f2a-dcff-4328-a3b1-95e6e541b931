// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Perception_Spatial_1_H
#define WINRT_Windows_Perception_Spatial_1_H
#include "winrt/impl/Windows.Perception.Spatial.0.h"
WINRT_EXPORT namespace winrt::Windows::Perception::Spatial
{
    struct __declspec(empty_bases) ISpatialAnchor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchor>
    {
        ISpatialAnchor(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchor2>
    {
        ISpatialAnchor2(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorExportSufficiency :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorExportSufficiency>
    {
        ISpatialAnchorExportSufficiency(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorExportSufficiency(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorExporter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorExporter>
    {
        ISpatialAnchorExporter(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorExporter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorExporterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorExporterStatics>
    {
        ISpatialAnchorExporterStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorExporterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorManagerStatics>
    {
        ISpatialAnchorManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorRawCoordinateSystemAdjustedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorRawCoordinateSystemAdjustedEventArgs>
    {
        ISpatialAnchorRawCoordinateSystemAdjustedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorRawCoordinateSystemAdjustedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorStatics>
    {
        ISpatialAnchorStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorStore :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorStore>
    {
        ISpatialAnchorStore(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorStore(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialAnchorTransferManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialAnchorTransferManagerStatics>
    {
        ISpatialAnchorTransferManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialAnchorTransferManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialBoundingVolume :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialBoundingVolume>
    {
        ISpatialBoundingVolume(std::nullptr_t = nullptr) noexcept {}
        ISpatialBoundingVolume(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialBoundingVolumeStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialBoundingVolumeStatics>
    {
        ISpatialBoundingVolumeStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialBoundingVolumeStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialCoordinateSystem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialCoordinateSystem>
    {
        ISpatialCoordinateSystem(std::nullptr_t = nullptr) noexcept {}
        ISpatialCoordinateSystem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntity>
    {
        ISpatialEntity(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntityAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntityAddedEventArgs>
    {
        ISpatialEntityAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntityAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntityFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntityFactory>
    {
        ISpatialEntityFactory(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntityFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntityRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntityRemovedEventArgs>
    {
        ISpatialEntityRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntityRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntityStore :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntityStore>
    {
        ISpatialEntityStore(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntityStore(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntityStoreStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntityStoreStatics>
    {
        ISpatialEntityStoreStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntityStoreStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntityUpdatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntityUpdatedEventArgs>
    {
        ISpatialEntityUpdatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntityUpdatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialEntityWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialEntityWatcher>
    {
        ISpatialEntityWatcher(std::nullptr_t = nullptr) noexcept {}
        ISpatialEntityWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialLocation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialLocation>
    {
        ISpatialLocation(std::nullptr_t = nullptr) noexcept {}
        ISpatialLocation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialLocation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialLocation2>
    {
        ISpatialLocation2(std::nullptr_t = nullptr) noexcept {}
        ISpatialLocation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialLocator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialLocator>
    {
        ISpatialLocator(std::nullptr_t = nullptr) noexcept {}
        ISpatialLocator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialLocatorAttachedFrameOfReference :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialLocatorAttachedFrameOfReference>
    {
        ISpatialLocatorAttachedFrameOfReference(std::nullptr_t = nullptr) noexcept {}
        ISpatialLocatorAttachedFrameOfReference(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialLocatorPositionalTrackingDeactivatingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialLocatorPositionalTrackingDeactivatingEventArgs>
    {
        ISpatialLocatorPositionalTrackingDeactivatingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISpatialLocatorPositionalTrackingDeactivatingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialLocatorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialLocatorStatics>
    {
        ISpatialLocatorStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialLocatorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialStageFrameOfReference :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialStageFrameOfReference>
    {
        ISpatialStageFrameOfReference(std::nullptr_t = nullptr) noexcept {}
        ISpatialStageFrameOfReference(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialStageFrameOfReferenceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialStageFrameOfReferenceStatics>
    {
        ISpatialStageFrameOfReferenceStatics(std::nullptr_t = nullptr) noexcept {}
        ISpatialStageFrameOfReferenceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpatialStationaryFrameOfReference :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpatialStationaryFrameOfReference>
    {
        ISpatialStationaryFrameOfReference(std::nullptr_t = nullptr) noexcept {}
        ISpatialStationaryFrameOfReference(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
