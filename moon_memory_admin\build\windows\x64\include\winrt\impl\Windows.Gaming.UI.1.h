// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Gaming_UI_1_H
#define WINRT_Windows_Gaming_UI_1_H
#include "winrt/impl/Windows.Gaming.UI.0.h"
WINRT_EXPORT namespace winrt::Windows::Gaming::UI
{
    struct __declspec(empty_bases) IGameBarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameBarStatics>
    {
        IGameBarStatics(std::nullptr_t = nullptr) noexcept {}
        IGameBarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameChatOverlay :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameChatOverlay>
    {
        IGameChatOverlay(std::nullptr_t = nullptr) noexcept {}
        IGameChatOverlay(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameChatOverlayStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameChatOverlayStatics>
    {
        IGameChatOverlayStatics(std::nullptr_t = nullptr) noexcept {}
        IGameChatOverlayStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
