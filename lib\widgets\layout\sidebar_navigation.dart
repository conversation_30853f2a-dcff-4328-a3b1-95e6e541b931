import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../screens/main_layout.dart';

class SidebarNavigation extends ConsumerWidget {
  const SidebarNavigation({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPage = ref.watch(currentPageProvider);
    final isCollapsed = ref.watch(sidebarCollapsedProvider);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      width: isCollapsed ? 80 : 280,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        border: Border(
          left: BorderSide(
            color: AppColors.borderColor,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(-2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // شعار التطبيق مع زر الطي
          _buildAppHeader(ref, isCollapsed),

          // قائمة التنقل
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: [
                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 0,
                  icon: Icons.dashboard,
                  title: 'لوحة المراقبة',
                  isSelected: currentPage == 0,
                  isCollapsed: isCollapsed,
                ),

                const SizedBox(height: 8),
                if (!isCollapsed) _buildSectionHeader('إدارة النظام'),

                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 1,
                  icon: Icons.people,
                  title: 'إدارة المستخدمين',
                  isSelected: currentPage == 1,
                  isCollapsed: isCollapsed,
                ),

                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 2,
                  icon: Icons.location_on,
                  title: 'إدارة المواقع',
                  isSelected: currentPage == 2,
                  isCollapsed: isCollapsed,
                ),

                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 4,
                  icon: Icons.devices,
                  title: 'إدارة الأجهزة',
                  isSelected: currentPage == 4,
                  isCollapsed: isCollapsed,
                ),

                const SizedBox(height: 8),
                if (!isCollapsed) _buildSectionHeader('المحتوى'),

                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 3,
                  icon: Icons.photo_library,
                  title: 'معرض الوسائط',
                  isSelected: currentPage == 3,
                  isCollapsed: isCollapsed,
                ),

                const SizedBox(height: 8),
                if (!isCollapsed) _buildSectionHeader('التقارير والإعدادات'),

                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 5,
                  icon: Icons.analytics,
                  title: 'التقارير',
                  isSelected: currentPage == 5,
                  isCollapsed: isCollapsed,
                ),

                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 6,
                  icon: Icons.settings,
                  title: 'الإعدادات',
                  isSelected: currentPage == 6,
                  isCollapsed: isCollapsed,
                ),

                const SizedBox(height: 8),
                if (!isCollapsed) _buildSectionHeader('أدوات المطور'),

                _buildNavigationItem(
                  context: context,
                  ref: ref,
                  index: 7,
                  icon: Icons.bug_report,
                  title: 'فحص قاعدة البيانات',
                  isSelected: currentPage == 7,
                  isCollapsed: isCollapsed,
                ),
              ],
            ),
          ),
          
          // معلومات التطبيق
          if (!isCollapsed) _buildAppFooter(),
        ],
      ),
    );
  }

  Widget _buildAppHeader(WidgetRef ref, bool isCollapsed) {
    return Container(
      padding: EdgeInsets.all(isCollapsed ? 12 : 24),
      decoration: BoxDecoration(
        gradient: AppColors.goldGradient,
      ),
      child: Column(
        children: [
          isCollapsed
            ? Center(
                child: Column(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppColors.darkBackground,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.admin_panel_settings,
                        size: 20,
                        color: AppColors.primaryGold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    IconButton(
                      onPressed: () {
                        ref.read(sidebarCollapsedProvider.notifier).state = false;
                      },
                      icon: const Icon(
                        Icons.menu_open,
                        color: AppColors.darkBackground,
                        size: 16,
                      ),
                      tooltip: 'توسيع القائمة',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ),
              )
            : Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppColors.darkBackground,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.admin_panel_settings,
                      size: 32,
                      color: AppColors.primaryGold,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppConstants.appName,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.darkBackground,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'نظام إدارة شامل',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColors.darkBackground.withValues(alpha: 0.8),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      ref.read(sidebarCollapsedProvider.notifier).state = true;
                    },
                    icon: const Icon(
                      Icons.menu,
                      color: AppColors.darkBackground,
                      size: 18,
                    ),
                    tooltip: 'طي القائمة',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: AppColors.mutedText,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildNavigationItem({
    required BuildContext context,
    required WidgetRef ref,
    required int index,
    required IconData icon,
    required String title,
    required bool isSelected,
    required bool isCollapsed,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isCollapsed ? 8 : 12, vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: Tooltip(
          message: isCollapsed ? title : '',
          child: InkWell(
            onTap: () {
              ref.read(currentPageProvider.notifier).state = index;
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isCollapsed ? 8 : 16,
                vertical: 12
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primaryGold.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                border: isSelected
                    ? Border.all(
                        color: AppColors.primaryGold.withValues(alpha: 0.3),
                        width: 1,
                      )
                    : null,
              ),
              child: isCollapsed
                  ? Center(
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppColors.primaryGold.withValues(alpha: 0.2)
                              : AppColors.surfaceBackground,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          icon,
                          size: 20,
                          color: isSelected
                              ? AppColors.primaryGold
                              : AppColors.secondaryText,
                        ),
                      ),
                    )
                  : Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.primaryGold.withValues(alpha: 0.2)
                                : AppColors.surfaceBackground,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            icon,
                            size: 20,
                            color: isSelected
                                ? AppColors.primaryGold
                                : AppColors.secondaryText,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            title,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                              color: isSelected
                                  ? AppColors.primaryGold
                                  : AppColors.primaryText,
                            ),
                          ),
                        ),
                        if (isSelected)
                          Container(
                            width: 4,
                            height: 20,
                            decoration: BoxDecoration(
                              color: AppColors.primaryGold,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                      ],
                    ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        border: Border(
          top: BorderSide(
            color: AppColors.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: AppColors.mutedText,
              ),
              const SizedBox(width: 8),
              Text(
                'الإصدار ${AppConstants.appVersion}',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.mutedText,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '© 2025 Moon Memory System',
            style: TextStyle(
              fontSize: 10,
              color: AppColors.mutedText,
            ),
          ),
        ],
      ),
    );
  }
}
