// 📱 مثال على كيفية دمج خدمة الجلسات في تطبيق الكاميرا

import 'package:flutter/material.dart';
import 'camera_app_session_service.dart';

class CameraAppIntegration {
  
  // 🔑 1. عند تسجيل الدخول
  static Future<void> onUserLogin({
    required String userId,
    required String deviceName,
    required String deviceModel, 
    required String deviceBrand,
    String? locationName,
    double? latitude,
    double? longitude,
  }) async {
    
    await CameraSessionService.instance.startSession(
      userId: userId,
      deviceName: deviceName,
      deviceModel: deviceModel,
      deviceBrand: deviceBrand,
      locationName: locationName ?? 'غير محدد',
      latitude: latitude,
      longitude: longitude,
    );
    
    debugPrint('✅ تم تسجيل دخول المستخدم وبدء الجلسة');
  }
  
  // 📸 2. عند التقاط صورة
  static Future<void> onPhotoCapture({
    required String photoId,
    String? location,
    Map<String, dynamic>? additionalData,
  }) async {
    
    await CameraSessionService.instance.logActivity(
      'photo_capture',
      metadata: {
        'photo_id': photoId,
        'location': location,
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalData,
      },
    );
    
    debugPrint('📸 تم تسجيل التقاط صورة: $photoId');
  }
  
  // 🎥 3. عند تسجيل فيديو
  static Future<void> onVideoRecord({
    required String videoId,
    required int durationSeconds,
    String? location,
    Map<String, dynamic>? additionalData,
  }) async {
    
    await CameraSessionService.instance.logActivity(
      'video_record',
      metadata: {
        'video_id': videoId,
        'duration_seconds': durationSeconds,
        'location': location,
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalData,
      },
    );
    
    debugPrint('🎥 تم تسجيل فيديو: $videoId');
  }
  
  // 📤 4. عند رفع ملف
  static Future<void> onFileUpload({
    required String fileId,
    required String fileType, // 'photo' or 'video'
    required int fileSizeBytes,
    String? location,
  }) async {
    
    await CameraSessionService.instance.logActivity(
      'file_upload',
      metadata: {
        'file_id': fileId,
        'file_type': fileType,
        'file_size_bytes': fileSizeBytes,
        'location': location,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    debugPrint('📤 تم رفع ملف: $fileId');
  }
  
  // 🚪 5. عند تسجيل الخروج
  static Future<void> onUserLogout() async {
    await CameraSessionService.instance.endSession();
    debugPrint('🚪 تم تسجيل خروج المستخدم وإنهاء الجلسة');
  }
  
  // 📱 6. عند إغلاق التطبيق
  static Future<void> onAppClose() async {
    await CameraSessionService.instance.endSession();
    debugPrint('📱 تم إغلاق التطبيق وإنهاء الجلسة');
  }
}

// 📱 مثال على الاستخدام في main.dart لتطبيق الكاميرا
class CameraAppExample extends StatefulWidget {
  @override
  _CameraAppExampleState createState() => _CameraAppExampleState();
}

class _CameraAppExampleState extends State<CameraAppExample> with WidgetsBindingObserver {
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  
  // التعامل مع دورة حياة التطبيق
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        // إنهاء الجلسة عند إغلاق التطبيق
        CameraAppIntegration.onAppClose();
        break;
      case AppLifecycleState.resumed:
        // يمكن إعادة تفعيل الجلسة هنا إذا لزم الأمر
        break;
      default:
        break;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('تطبيق الكاميرا')),
      body: Column(
        children: [
          ElevatedButton(
            onPressed: _simulateLogin,
            child: Text('تسجيل الدخول'),
          ),
          ElevatedButton(
            onPressed: _simulatePhotoCapture,
            child: Text('التقاط صورة'),
          ),
          ElevatedButton(
            onPressed: _simulateVideoRecord,
            child: Text('تسجيل فيديو'),
          ),
          ElevatedButton(
            onPressed: _simulateLogout,
            child: Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
  
  void _simulateLogin() async {
    await CameraAppIntegration.onUserLogin(
      userId: '81b3d566-d65d-44b6-902e-84dfb32588ae', // ID المستخدم anan
      deviceName: 'My Phone',
      deviceModel: 'Pixel 7 Pro',
      deviceBrand: 'Google',
      locationName: 'الرياض، السعودية',
      latitude: 24.7136,
      longitude: 46.6753,
    );
  }
  
  void _simulatePhotoCapture() async {
    await CameraAppIntegration.onPhotoCapture(
      photoId: 'photo_${DateTime.now().millisecondsSinceEpoch}',
      location: 'الرياض، السعودية',
    );
  }
  
  void _simulateVideoRecord() async {
    await CameraAppIntegration.onVideoRecord(
      videoId: 'video_${DateTime.now().millisecondsSinceEpoch}',
      durationSeconds: 30,
      location: 'الرياض، السعودية',
    );
  }
  
  void _simulateLogout() async {
    await CameraAppIntegration.onUserLogout();
  }
}

/*
📋 خطوات التطبيق:

1. أضف camera_app_session_service.dart إلى تطبيق الكاميرا
2. استدعي CameraAppIntegration.onUserLogin() عند تسجيل الدخول
3. استدعي CameraAppIntegration.onPhotoCapture() عند التقاط صورة
4. استدعي CameraAppIntegration.onVideoRecord() عند تسجيل فيديو
5. استدعي CameraAppIntegration.onUserLogout() عند تسجيل الخروج
6. أضف WidgetsBindingObserver للتعامل مع إغلاق التطبيق

🎯 النتيجة:
- سيظهر المستخدم كمتصل في تطبيق الإدارة
- سيتم تحديث last_seen كل دقيقة
- سيتم تسجيل جميع الأنشطة
- سيتم إنهاء الجلسة عند الخروج أو إغلاق التطبيق
*/
