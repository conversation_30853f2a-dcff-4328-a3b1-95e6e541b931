import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';
import '../services/admin_supabase_service.dart';
import '../models/user_model.dart';

// Users Filter Model
class UsersFilter {
  final String? searchQuery;
  final bool? isActive;
  final bool? isAdmin;
  final String? department;
  final String? accountType;

  const UsersFilter({
    this.searchQuery,
    this.isActive,
    this.isAdmin,
    this.department,
    this.accountType,
  });

  UsersFilter copyWith({
    String? searchQuery,
    bool? isActive,
    bool? isAdmin,
    String? department,
    String? accountType,
  }) {
    return UsersFilter(
      searchQuery: searchQuery ?? this.searchQuery,
      isActive: isActive ?? this.isActive,
      isAdmin: isAdmin ?? this.isAdmin,
      department: department ?? this.department,
      accountType: accountType ?? this.accountType,
    );
  }

  bool get hasActiveFilters =>
      searchQuery?.isNotEmpty == true ||
      isActive != null ||
      isAdmin != null ||
      department?.isNotEmpty == true ||
      accountType?.isNotEmpty == true;
}

// Users State
class UsersState {
  final List<UserModel> users;
  final bool isLoading;
  final String? error;
  final UsersFilter filter;
  final int totalCount;
  final int currentPage;
  final int pageSize;

  const UsersState({
    required this.users,
    required this.isLoading,
    this.error,
    required this.filter,
    required this.totalCount,
    required this.currentPage,
    required this.pageSize,
  });

  UsersState copyWith({
    List<UserModel>? users,
    bool? isLoading,
    String? error,
    UsersFilter? filter,
    int? totalCount,
    int? currentPage,
    int? pageSize,
  }) {
    return UsersState(
      users: users ?? this.users,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      filter: filter ?? this.filter,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
    );
  }

  int get totalPages => (totalCount / pageSize).ceil();
  bool get hasNextPage => currentPage < totalPages;
  bool get hasPreviousPage => currentPage > 1;
}

// Users Provider
class UsersNotifier extends StateNotifier<UsersState> {
  RealtimeChannel? _subscription;

  UsersNotifier() : super(const UsersState(
    users: [],
    isLoading: false,
    filter: UsersFilter(),
    totalCount: 0,
    currentPage: 1,
    pageSize: 20,
  )) {
    loadUsers();
    _setupRealtimeSubscription();
  }

  @override
  void dispose() {
    _subscription?.unsubscribe();
    super.dispose();
  }

  Future<void> loadUsers({bool reset = false}) async {
    if (reset) {
      state = state.copyWith(currentPage: 1);
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final supabase = SupabaseService.instance.client;

      // بناء الاستعلام مع الفلاتر
      var query = supabase.from('users').select();

      // تطبيق فلتر البحث
      if (state.filter.searchQuery?.isNotEmpty == true) {
        final searchTerm = '%${state.filter.searchQuery}%';
        final searchFilter = 'full_name.ilike.$searchTerm,national_id.ilike.$searchTerm,email.ilike.$searchTerm,department.ilike.$searchTerm,position.ilike.$searchTerm';
        query = query.or(searchFilter);
      }

      // تطبيق فلتر الحالة النشطة
      if (state.filter.isActive != null) {
        query = query.eq('is_active', state.filter.isActive!);
      }

      // تطبيق فلتر نوع المدير
      if (state.filter.isAdmin != null) {
        query = query.eq('is_admin', state.filter.isAdmin!);
      }

      // تطبيق فلتر القسم
      if (state.filter.department?.isNotEmpty == true) {
        query = query.eq('department', state.filter.department!);
      }

      // تطبيق فلتر نوع الحساب
      if (state.filter.accountType?.isNotEmpty == true) {
        query = query.eq('account_type', state.filter.accountType!);
      }

      // جلب العدد الإجمالي أولاً (استعلام منفصل)
      var countQuery = supabase.from('users').select('id');

      // تطبيق نفس الفلاتر على استعلام العد
      if (state.filter.searchQuery?.isNotEmpty == true) {
        final searchTerm = '%${state.filter.searchQuery}%';
        final searchFilter = 'full_name.ilike.$searchTerm,national_id.ilike.$searchTerm,email.ilike.$searchTerm,department.ilike.$searchTerm,position.ilike.$searchTerm';
        countQuery = countQuery.or(searchFilter);
      }

      if (state.filter.isActive != null) {
        countQuery = countQuery.eq('is_active', state.filter.isActive!);
      }

      if (state.filter.isAdmin != null) {
        countQuery = countQuery.eq('is_admin', state.filter.isAdmin!);
      }

      if (state.filter.department?.isNotEmpty == true) {
        countQuery = countQuery.eq('department', state.filter.department!);
      }

      if (state.filter.accountType?.isNotEmpty == true) {
        countQuery = countQuery.eq('account_type', state.filter.accountType!);
      }

      final countResponse = await countQuery;
      final totalCount = countResponse.length;

      // تطبيق التصفح بالصفحات والترتيب
      final offset = (state.currentPage - 1) * state.pageSize;
      final response = await query
          .order('created_at', ascending: false)
          .range(offset, offset + state.pageSize - 1);

      final users = response.map((json) => UserModel.fromJson(json)).toList();

      state = state.copyWith(
        users: users,
        isLoading: false,
        totalCount: totalCount,
      );

    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  void updateFilter(UsersFilter newFilter) {
    state = state.copyWith(filter: newFilter, currentPage: 1);
    loadUsers();
  }

  void clearFilter() {
    state = state.copyWith(filter: const UsersFilter(), currentPage: 1);
    loadUsers();
  }

  void nextPage() {
    if (state.hasNextPage) {
      state = state.copyWith(currentPage: state.currentPage + 1);
      loadUsers();
    }
  }

  void previousPage() {
    if (state.hasPreviousPage) {
      state = state.copyWith(currentPage: state.currentPage - 1);
      loadUsers();
    }
  }

  void goToPage(int page) {
    if (page >= 1 && page <= state.totalPages) {
      state = state.copyWith(currentPage: page);
      loadUsers();
    }
  }

  Future<void> toggleUserStatus(String userId, bool isActive) async {
    try {
      // استخدام الخدمة الإدارية مع Service Role
      final result = await AdminSupabaseService.instance.toggleUserStatus(
        userId: userId,
        isActive: isActive,
      );

      if (result['success']) {
        // تحديث القائمة المحلية فوراً
        final updatedUsers = state.users.map((user) {
          if (user.id == userId) {
            return user.copyWith(isActive: isActive);
          }
          return user;
        }).toList();

        state = state.copyWith(users: updatedUsers, error: null);

        // إعادة تحميل البيانات للتأكد من التحديث
        await loadUsers();
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }

    } catch (error) {
      state = state.copyWith(error: 'فشل في تغيير حالة المستخدم: ${error.toString()}');
      rethrow;
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      // حفظ المستخدم للتراجع في حالة الفشل
      final userToDelete = state.users.firstWhere((user) => user.id == userId);

      // إزالة المستخدم من القائمة المحلية فوراً (للاستجابة السريعة)
      final updatedUsers = state.users.where((user) => user.id != userId).toList();
      state = state.copyWith(users: updatedUsers, error: null);

      // استخدام الخدمة الإدارية للحذف الفعلي
      final result = await AdminSupabaseService.instance.deleteUser(
        userId: userId,
      );

      if (result['success']) {
        // تحديث العدد الإجمالي
        state = state.copyWith(totalCount: state.totalCount - 1);

        // إعادة تحميل القائمة للتأكد من التحديث
        await loadUsers();
      } else {
        // إعادة المستخدم للقائمة في حالة الفشل
        final restoredUsers = [...state.users, userToDelete];
        restoredUsers.sort((a, b) => (b.createdAt ?? DateTime.now()).compareTo(a.createdAt ?? DateTime.now()));
        state = state.copyWith(users: restoredUsers, error: result['error']);
        throw Exception(result['error']);
      }

    } catch (error) {
      state = state.copyWith(error: 'فشل في حذف المستخدم: ${error.toString()}');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createUser({
    required String nationalId,
    required String fullName,
    String? phone,
    required String department,
    required String position,
    required String password,
    required bool isAdmin,
    required bool isActive,
  }) async {
    try {
      // إظهار حالة التحميل
      state = state.copyWith(isLoading: true, error: null);

      // استخدام الخدمة الإدارية لإنشاء المستخدم
      final result = await AdminSupabaseService.instance.createUser(
        nationalId: nationalId,
        fullName: fullName,
        phone: phone,
        department: department,
        position: position,
        password: password,
        isAdmin: isAdmin,
        isActive: isActive,
      );

      if (result['success']) {
        // إعادة تحميل القائمة مع إعادة تعيين للصفحة الأولى
        state = state.copyWith(currentPage: 1);
        await loadUsers();

        // مسح رسالة الخطأ السابقة
        state = state.copyWith(error: null, isLoading: false);
      } else {
        state = state.copyWith(error: result['error'], isLoading: false);
      }

      return result;

    } catch (error) {
      final errorMessage = 'فشل في إنشاء المستخدم: ${error.toString()}';
      state = state.copyWith(error: errorMessage, isLoading: false);
      return {
        'success': false,
        'error': errorMessage,
      };
    }
  }

  /// حذف مستخدم بالرقم الوطني (للتنظيف)
  Future<void> deleteUserByNationalId(String nationalId) async {
    try {
      final result = await AdminSupabaseService.instance.deleteUserByNationalId(
        nationalId: nationalId,
      );

      if (result['success']) {
        await loadUsers();
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }

    } catch (error) {
      state = state.copyWith(error: 'فشل في حذف المستخدم: ${error.toString()}');
      rethrow;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(error: null);
  }

  void refresh() {
    loadUsers(reset: true);
  }

  /// إعداد الاشتراك في التحديثات المباشرة
  void _setupRealtimeSubscription() {
    try {
      final supabase = SupabaseService.instance.client;

      _subscription = supabase
          .channel('users_changes')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'users',
            callback: (payload) {
              _handleRealtimeUpdate(payload);
            },
          )
          .subscribe();

    } catch (error) {
      // في حالة فشل الاشتراك، نستمر بدون Real-time
      debugPrint('فشل في إعداد Real-time subscription: $error');
    }
  }

  /// معالجة التحديثات المباشرة
  void _handleRealtimeUpdate(PostgresChangePayload payload) {
    try {
      switch (payload.eventType) {
        case PostgresChangeEvent.insert:
          _handleUserInserted(payload.newRecord);
          break;
        case PostgresChangeEvent.update:
          _handleUserUpdated(payload.newRecord);
          break;
        case PostgresChangeEvent.delete:
          _handleUserDeleted(payload.oldRecord);
          break;
        case PostgresChangeEvent.all:
          // لا نحتاج معالجة خاصة لهذا النوع
          break;
      }
    } catch (error) {
      // في حالة خطأ في معالجة التحديث، نعيد تحميل البيانات
      debugPrint('خطأ في معالجة Real-time update: $error');
      loadUsers();
    }
  }

  /// معالجة إدراج مستخدم جديد
  void _handleUserInserted(Map<String, dynamic> record) {
    try {
      final newUser = UserModel.fromJson(record);

      // إضافة المستخدم الجديد للقائمة إذا كان يطابق الفلاتر الحالية
      if (_userMatchesCurrentFilter(newUser)) {
        final updatedUsers = [newUser, ...state.users];
        state = state.copyWith(
          users: updatedUsers,
          totalCount: state.totalCount + 1,
        );
      } else {
        // تحديث العدد الإجمالي فقط
        state = state.copyWith(totalCount: state.totalCount + 1);
      }
    } catch (error) {
      debugPrint('خطأ في معالجة إدراج المستخدم: $error');
    }
  }

  /// معالجة تحديث مستخدم
  void _handleUserUpdated(Map<String, dynamic> record) {
    try {
      final updatedUser = UserModel.fromJson(record);

      final updatedUsers = state.users.map((user) {
        if (user.id == updatedUser.id) {
          return updatedUser;
        }
        return user;
      }).toList();

      state = state.copyWith(users: updatedUsers);
    } catch (error) {
      debugPrint('خطأ في معالجة تحديث المستخدم: $error');
    }
  }

  /// معالجة حذف مستخدم
  void _handleUserDeleted(Map<String, dynamic> record) {
    try {
      final deletedUserId = record['id'] as String;

      final updatedUsers = state.users.where((user) => user.id != deletedUserId).toList();
      state = state.copyWith(
        users: updatedUsers,
        totalCount: state.totalCount - 1,
      );
    } catch (error) {
      debugPrint('خطأ في معالجة حذف المستخدم: $error');
    }
  }

  /// التحقق من مطابقة المستخدم للفلاتر الحالية
  bool _userMatchesCurrentFilter(UserModel user) {
    final filter = state.filter;

    // فلتر البحث
    if (filter.searchQuery?.isNotEmpty == true) {
      final query = filter.searchQuery!.toLowerCase();
      final matches = user.fullName?.toLowerCase().contains(query) == true ||
                     user.nationalId?.toLowerCase().contains(query) == true ||
                     user.email?.toLowerCase().contains(query) == true ||
                     user.department?.toLowerCase().contains(query) == true ||
                     user.position?.toLowerCase().contains(query) == true;
      if (!matches) return false;
    }

    // فلتر الحالة النشطة
    if (filter.isActive != null && user.isActive != filter.isActive) {
      return false;
    }

    // فلتر نوع المدير
    if (filter.isAdmin != null && user.isAdmin != filter.isAdmin) {
      return false;
    }

    // فلتر القسم
    if (filter.department?.isNotEmpty == true && user.department != filter.department) {
      return false;
    }

    // فلتر نوع الحساب
    if (filter.accountType?.isNotEmpty == true && user.accountType != filter.accountType) {
      return false;
    }

    return true;
  }

  /// تطبيق فلتر البحث
  void setSearchQuery(String query) {
    state = state.copyWith(
      filter: state.filter.copyWith(searchQuery: query),
      currentPage: 1, // إعادة تعيين الصفحة للأولى
    );
    loadUsers();
  }

  /// تطبيق فلتر الحالة النشطة
  void setActiveFilter(bool? isActive) {
    state = state.copyWith(
      filter: state.filter.copyWith(isActive: isActive),
      currentPage: 1,
    );
    loadUsers();
  }

  /// تطبيق فلتر نوع المدير
  void setAdminFilter(bool? isAdmin) {
    state = state.copyWith(
      filter: state.filter.copyWith(isAdmin: isAdmin),
      currentPage: 1,
    );
    loadUsers();
  }

  /// تطبيق فلتر القسم
  void setDepartmentFilter(String? department) {
    state = state.copyWith(
      filter: state.filter.copyWith(department: department),
      currentPage: 1,
    );
    loadUsers();
  }

  /// تطبيق فلتر نوع الحساب
  void setAccountTypeFilter(String? accountType) {
    state = state.copyWith(
      filter: state.filter.copyWith(accountType: accountType),
      currentPage: 1,
    );
    loadUsers();
  }

  /// مسح جميع الفلاتر
  void clearFilters() {
    state = state.copyWith(
      filter: const UsersFilter(),
      currentPage: 1,
    );
    loadUsers();
  }




}

// Provider
final usersProvider = StateNotifierProvider<UsersNotifier, UsersState>((ref) {
  return UsersNotifier();
});

// Helper providers
final filteredUsersCountProvider = Provider<int>((ref) {
  final usersState = ref.watch(usersProvider);
  return usersState.totalCount;
});

final activeUsersCountProvider = Provider<int>((ref) {
  final usersState = ref.watch(usersProvider);
  return usersState.users.where((user) => user.isActive).length;
});

final adminUsersCountProvider = Provider<int>((ref) {
  final usersState = ref.watch(usersProvider);
  return usersState.users.where((user) => user.isAdmin).length;
});


