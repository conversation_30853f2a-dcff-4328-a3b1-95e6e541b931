// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Media_Core_Preview_1_H
#define WINRT_Windows_Media_Core_Preview_1_H
#include "winrt/impl/Windows.Media.Core.Preview.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::Core::Preview
{
    struct __declspec(empty_bases) ISoundLevelBrokerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISoundLevelBrokerStatics>
    {
        ISoundLevelBrokerStatics(std::nullptr_t = nullptr) noexcept {}
        ISoundLevelBrokerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
