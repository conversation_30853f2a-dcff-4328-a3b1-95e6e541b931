import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/location_model.dart';
import '../models/device_model.dart';
import 'package:flutter/foundation.dart';

class LocationsService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// جلب جميع المواقع مع إحداثياتها
  Future<List<LocationModel>> getLocationsWithCoordinates() async {
    try {
      debugPrint('🔍 جلب المواقع مع الإحداثيات...');
      
      final response = await _supabase
          .from('locations')
          .select('*')
          .order('sort_order');

      final locations = (response as List)
          .map((json) => LocationModel.fromJson(json))
          .toList();

      // إضافة إحداثيات تجريبية للمواقع التي لا تحتوي على إحداثيات
      final locationsWithCoords = await _addDummyCoordinates(locations);
      
      debugPrint('✅ تم جلب ${locationsWithCoords.length} موقع');
      return locationsWithCoords;
    } catch (error) {
      debugPrint('❌ خطأ في جلب المواقع: $error');
      
      // إرجاع مواقع تجريبية في حالة الخطأ
      return _createDummyLocations();
    }
  }

  /// جلب الأجهزة في موقع معين
  Future<List<DeviceModel>> getDevicesInLocation(String locationId) async {
    try {
      debugPrint('🔍 جلب الأجهزة في الموقع: $locationId');
      
      final response = await _supabase
          .from('devices')
          .select('*')
          .eq('location_id', locationId);

      final devices = (response as List)
          .map((json) => DeviceModel.fromJson(json))
          .toList();
      
      debugPrint('✅ تم جلب ${devices.length} جهاز في الموقع');
      return devices;
    } catch (error) {
      debugPrint('❌ خطأ في جلب أجهزة الموقع: $error');
      return [];
    }
  }

  /// إضافة إحداثيات تجريبية للمواقع
  Future<List<LocationModel>> _addDummyCoordinates(List<LocationModel> locations) async {
    final locationsWithCoords = <LocationModel>[];
    
    // إحداثيات تجريبية لمواقع مختلفة في السعودية
    final dummyCoordinates = [
      {'lat': 24.7136, 'lng': 46.6753, 'address': 'الرياض، المملكة العربية السعودية'},
      {'lat': 21.3891, 'lng': 39.8579, 'address': 'مكة المكرمة، المملكة العربية السعودية'},
      {'lat': 24.4539, 'lng': 39.6775, 'address': 'المدينة المنورة، المملكة العربية السعودية'},
      {'lat': 26.4207, 'lng': 50.0888, 'address': 'الدمام، المملكة العربية السعودية'},
      {'lat': 21.4858, 'lng': 39.1925, 'address': 'جدة، المملكة العربية السعودية'},
      {'lat': 25.3548, 'lng': 49.5834, 'address': 'الخبر، المملكة العربية السعودية'},
      {'lat': 27.5114, 'lng': 41.7208, 'address': 'حائل، المملكة العربية السعودية'},
      {'lat': 18.2465, 'lng': 42.5326, 'address': 'أبها، المملكة العربية السعودية'},
    ];

    for (int i = 0; i < locations.length; i++) {
      final location = locations[i];
      final coordIndex = i % dummyCoordinates.length;
      final coords = dummyCoordinates[coordIndex];
      
      // إضافة تنويع بسيط للإحداثيات لتجنب التداخل
      final latOffset = (i * 0.01) - 0.02;
      final lngOffset = (i * 0.01) - 0.02;
      
      locationsWithCoords.add(location.copyWith(
        latitude: (coords['lat'] as double) + latOffset,
        longitude: (coords['lng'] as double) + lngOffset,
        address: coords['address'] as String?,
        devicesCount: (i % 5) + 1, // عدد تجريبي للأجهزة
      ));
    }
    
    return locationsWithCoords;
  }

  /// إنشاء مواقع تجريبية
  List<LocationModel> _createDummyLocations() {
    return [
      LocationModel(
        id: '1',
        locationCode: 'U101',
        locationType: 'U',
        locationNumber: '101',
        locationNameAr: 'المبنى الرئيسي',
        locationNameEn: 'Main Building',
        sortOrder: 1,
        isActive: true,
        totalPhotos: 150,
        totalVideos: 45,
        latitude: 24.7136,
        longitude: 46.6753,
        address: 'الرياض، المملكة العربية السعودية',
        devicesCount: 5,
        deviceIds: ['1', '2', '3', '4', '5'],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      LocationModel(
        id: '2',
        locationCode: 'C201',
        locationType: 'C',
        locationNumber: '201',
        locationNameAr: 'كلية الهندسة',
        locationNameEn: 'Engineering College',
        sortOrder: 2,
        isActive: true,
        totalPhotos: 89,
        totalVideos: 23,
        latitude: 24.7200,
        longitude: 46.6800,
        address: 'الرياض، المملكة العربية السعودية',
        devicesCount: 3,
        deviceIds: ['6', '7', '8'],
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now(),
      ),
      LocationModel(
        id: '3',
        locationCode: 'U102',
        locationType: 'U',
        locationNumber: '102',
        locationNameAr: 'مبنى الإدارة',
        locationNameEn: 'Administration Building',
        sortOrder: 3,
        isActive: true,
        totalPhotos: 67,
        totalVideos: 12,
        latitude: 24.7050,
        longitude: 46.6700,
        address: 'الرياض، المملكة العربية السعودية',
        devicesCount: 2,
        deviceIds: ['9', '10'],
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now(),
      ),
      LocationModel(
        id: '4',
        locationCode: 'C301',
        locationType: 'C',
        locationNumber: '301',
        locationNameAr: 'كلية الطب',
        locationNameEn: 'Medical College',
        sortOrder: 4,
        isActive: true,
        totalPhotos: 234,
        totalVideos: 78,
        latitude: 24.7300,
        longitude: 46.6900,
        address: 'الرياض، المملكة العربية السعودية',
        devicesCount: 7,
        deviceIds: ['11', '12', '13', '14', '15', '16', '17'],
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
      ),
      LocationModel(
        id: '5',
        locationCode: 'U103',
        locationType: 'U',
        locationNumber: '103',
        locationNameAr: 'المكتبة المركزية',
        locationNameEn: 'Central Library',
        sortOrder: 5,
        isActive: true,
        totalPhotos: 45,
        totalVideos: 8,
        latitude: 24.7000,
        longitude: 46.6650,
        address: 'الرياض، المملكة العربية السعودية',
        devicesCount: 1,
        deviceIds: ['18'],
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// حساب إحصائيات المواقع
  Future<Map<String, dynamic>> getLocationsStats() async {
    try {
      final locations = await getLocationsWithCoordinates();
      
      final totalLocations = locations.length;
      final activeLocations = locations.where((l) => l.isActive).length;
      final totalDevices = locations.fold<int>(0, (sum, l) => sum + (l.devicesCount ?? 0));
      final totalPhotos = locations.fold<int>(0, (sum, l) => sum + l.totalPhotos);
      final totalVideos = locations.fold<int>(0, (sum, l) => sum + l.totalVideos);
      
      return {
        'totalLocations': totalLocations,
        'activeLocations': activeLocations,
        'inactiveLocations': totalLocations - activeLocations,
        'totalDevices': totalDevices,
        'totalPhotos': totalPhotos,
        'totalVideos': totalVideos,
        'totalMedia': totalPhotos + totalVideos,
      };
    } catch (error) {
      debugPrint('❌ خطأ في حساب إحصائيات المواقع: $error');
      return {
        'totalLocations': 0,
        'activeLocations': 0,
        'inactiveLocations': 0,
        'totalDevices': 0,
        'totalPhotos': 0,
        'totalVideos': 0,
        'totalMedia': 0,
      };
    }
  }
}
