import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/device_model.dart';
import '../services/devices_service.dart';
import 'package:flutter/foundation.dart';

// حالة الأجهزة
class DevicesState {
  final List<DeviceModel> devices;
  final bool isLoading;
  final String? error;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final bool hasMore;
  final String? searchQuery;
  final String? statusFilter;
  final String? typeFilter;
  final String? userFilter;
  final String? locationFilter;

  const DevicesState({
    this.devices = const [],
    this.isLoading = false,
    this.error,
    this.totalCount = 0,
    this.currentPage = 1,
    this.totalPages = 0,
    this.hasMore = false,
    this.searchQuery,
    this.statusFilter,
    this.typeFilter,
    this.userFilter,
    this.locationFilter,
  });

  DevicesState copyWith({
    List<DeviceModel>? devices,
    bool? isLoading,
    String? error,
    int? totalCount,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
    String? searchQuery,
    String? statusFilter,
    String? typeFilter,
    String? userFilter,
    String? locationFilter,
  }) {
    return DevicesState(
      devices: devices ?? this.devices,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: statusFilter ?? this.statusFilter,
      typeFilter: typeFilter ?? this.typeFilter,
      userFilter: userFilter ?? this.userFilter,
      locationFilter: locationFilter ?? this.locationFilter,
    );
  }
}

// مزود الأجهزة
class DevicesNotifier extends StateNotifier<DevicesState> {
  DevicesNotifier() : super(const DevicesState()) {
    loadDevices();
  }

  final DevicesService _devicesService = DevicesService.instance;

  /// تحميل الأجهزة
  Future<void> loadDevices({
    int page = 1,
    bool reset = false,
  }) async {
    if (reset) {
      state = state.copyWith(
        devices: [],
        currentPage: 1,
        error: null,
      );
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _devicesService.getDevices(
        page: page,
        search: state.searchQuery,
        status: state.statusFilter,
        type: state.typeFilter,
        userId: state.userFilter,
        locationId: state.locationFilter,
      );

      if (result['success']) {
        final newDevices = result['devices'] as List<DeviceModel>;
        final devices = page == 1 ? newDevices : [...state.devices, ...newDevices];

        state = state.copyWith(
          devices: devices,
          totalCount: result['totalCount'],
          currentPage: result['currentPage'],
          totalPages: result['totalPages'],
          hasMore: result['hasMore'],
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: result['error'],
          isLoading: false,
        );
      }
    } catch (error) {
      debugPrint('❌ خطأ في تحميل الأجهزة: $error');

      // إضافة بيانات تجريبية في حالة الخطأ
      final dummyDevices = _createDummyDevices();

      state = state.copyWith(
        devices: dummyDevices,
        totalCount: dummyDevices.length,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
        isLoading: false,
        error: 'تم تحميل بيانات تجريبية - خطأ في الاتصال: ${error.toString()}',
      );
    }
  }

  /// البحث في الأجهزة
  Future<void> searchDevices(String query) async {
    state = state.copyWith(searchQuery: query);
    await loadDevices(reset: true);
  }

  /// فلترة الأجهزة حسب الحالة
  Future<void> filterByStatus(String? status) async {
    state = state.copyWith(statusFilter: status);
    await loadDevices(reset: true);
  }

  /// فلترة الأجهزة حسب النوع
  Future<void> filterByType(String? type) async {
    state = state.copyWith(typeFilter: type);
    await loadDevices(reset: true);
  }

  /// فلترة الأجهزة حسب المستخدم
  Future<void> filterByUser(String? userId) async {
    state = state.copyWith(userFilter: userId);
    await loadDevices(reset: true);
  }

  /// فلترة الأجهزة حسب الموقع
  Future<void> filterByLocation(String? locationId) async {
    state = state.copyWith(locationFilter: locationId);
    await loadDevices(reset: true);
  }

  /// مسح الفلاتر
  Future<void> clearFilters() async {
    state = state.copyWith(
      searchQuery: null,
      statusFilter: null,
      typeFilter: null,
      userFilter: null,
      locationFilter: null,
    );
    await loadDevices(reset: true);
  }

  /// إنشاء جهاز جديد
  Future<Map<String, dynamic>> createDevice({
    required String deviceId,
    required String deviceName,
    required String deviceType,
    required String model,
    required String userId,
    String? locationId,
    String? description,
    Map<String, dynamic>? specifications,
  }) async {
    try {
      final result = await _devicesService.createDevice(
        deviceId: deviceId,
        deviceName: deviceName,
        deviceType: deviceType,
        model: model,
        userId: userId,
        locationId: locationId,
        description: description,
        specifications: specifications,
      );

      if (result['success']) {
        await loadDevices(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
      }

      return result;
    } catch (error) {
      final errorMessage = 'فشل في إنشاء الجهاز: ${error.toString()}';
      state = state.copyWith(error: errorMessage);
      return {
        'success': false,
        'error': errorMessage,
      };
    }
  }

  /// تحديث جهاز
  Future<Map<String, dynamic>> updateDevice({
    required String deviceId,
    String? deviceName,
    String? deviceType,
    String? model,
    String? userId,
    String? locationId,
    String? description,
    String? status,
    Map<String, dynamic>? specifications,
  }) async {
    try {
      final result = await _devicesService.updateDevice(
        deviceId: deviceId,
        deviceName: deviceName,
        deviceType: deviceType,
        model: model,
        userId: userId,
        locationId: locationId,
        description: description,
        status: status,
        specifications: specifications,
      );

      if (result['success']) {
        await loadDevices(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
      }

      return result;
    } catch (error) {
      final errorMessage = 'فشل في تحديث الجهاز: ${error.toString()}';
      state = state.copyWith(error: errorMessage);
      return {
        'success': false,
        'error': errorMessage,
      };
    }
  }

  /// حذف جهاز
  Future<void> deleteDevice(String deviceId) async {
    try {
      final result = await _devicesService.deleteDevice(deviceId: deviceId);
      
      if (result['success']) {
        await loadDevices(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }
    } catch (error) {
      state = state.copyWith(error: 'فشل في حذف الجهاز: ${error.toString()}');
      rethrow;
    }
  }

  /// تغيير حالة الجهاز
  Future<void> toggleDeviceStatus(String deviceId) async {
    try {
      final result = await _devicesService.toggleDeviceStatus(deviceId: deviceId);
      
      if (result['success']) {
        await loadDevices(reset: true);
        state = state.copyWith(error: null);
      } else {
        state = state.copyWith(error: result['error']);
        throw Exception(result['error']);
      }
    } catch (error) {
      state = state.copyWith(error: 'فشل في تغيير حالة الجهاز: ${error.toString()}');
      rethrow;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// تحديث القائمة
  void refresh() {
    loadDevices(reset: true);
  }

  /// تحميل المزيد من الأجهزة
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;
    await loadDevices(page: state.currentPage + 1);
  }

  /// إنشاء بيانات تجريبية للأجهزة
  List<DeviceModel> _createDummyDevices() {
    return [
      DeviceModel(
        id: '1',
        deviceId: 'CAM001',
        deviceName: 'كاميرا المدخل الرئيسي',
        deviceType: 'camera',
        model: 'Canon EOS R5',
        userId: 'user1',
        locationId: 'loc1',
        description: 'كاميرا مراقبة المدخل الرئيسي',
        status: 'active',
        specifications: {
          'battery_level': 85,
          'serial_number': 'CAM001',
          'last_seen': DateTime.now().subtract(const Duration(minutes: 5)).toIso8601String(),
        },
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      DeviceModel(
        id: '2',
        deviceId: 'TAB002',
        deviceName: 'جهاز لوحي - الاستقبال',
        deviceType: 'tablet',
        model: 'iPad Pro',
        userId: 'user2',
        locationId: 'loc2',
        description: 'جهاز لوحي لمكتب الاستقبال',
        status: 'active',
        specifications: {
          'battery_level': 92,
          'serial_number': 'TAB002',
          'last_seen': DateTime.now().subtract(const Duration(minutes: 2)).toIso8601String(),
        },
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
      ),
      DeviceModel(
        id: '3',
        deviceId: 'PHN003',
        deviceName: 'هاتف ذكي - الأمن',
        deviceType: 'phone',
        model: 'iPhone 14 Pro',
        userId: 'user3',
        locationId: 'loc3',
        description: 'هاتف ذكي لنقطة الأمن',
        status: 'inactive',
        specifications: {
          'battery_level': 23,
          'serial_number': 'PHN003',
          'last_seen': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        },
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      DeviceModel(
        id: '4',
        deviceId: 'CAM004',
        deviceName: 'كاميرا المخرج الخلفي',
        deviceType: 'camera',
        model: 'Sony A7 IV',
        userId: 'user4',
        locationId: 'loc4',
        description: 'كاميرا مراقبة المخرج الخلفي',
        status: 'maintenance',
        specifications: {
          'battery_level': 0,
          'serial_number': 'CAM004',
          'last_seen': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        },
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      DeviceModel(
        id: '5',
        deviceId: 'TAB005',
        deviceName: 'جهاز لوحي - القاعة الكبرى',
        deviceType: 'tablet',
        model: 'Samsung Galaxy Tab S8',
        userId: 'user5',
        locationId: 'loc5',
        description: 'جهاز لوحي للقاعة الكبرى',
        status: 'active',
        specifications: {
          'battery_level': 78,
          'serial_number': 'TAB005',
          'last_seen': DateTime.now().subtract(const Duration(minutes: 10)).toIso8601String(),
        },
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now(),
      ),
    ];
  }
}

// مزودات الأجهزة
final devicesProvider = StateNotifierProvider<DevicesNotifier, DevicesState>((ref) {
  return DevicesNotifier();
});

// مزود إحصائيات الأجهزة
final devicesStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  return await DevicesService.instance.getDevicesStats();
});

// مزودات الإحصائيات المحددة
final totalDevicesCountProvider = Provider<int>((ref) {
  return ref.watch(devicesProvider).totalCount;
});

final activeDevicesCountProvider = Provider<int>((ref) {
  final devices = ref.watch(devicesProvider).devices;
  return devices.where((device) => device.status == 'active').length;
});

final inactiveDevicesCountProvider = Provider<int>((ref) {
  final devices = ref.watch(devicesProvider).devices;
  return devices.where((device) => device.status == 'inactive').length;
});
