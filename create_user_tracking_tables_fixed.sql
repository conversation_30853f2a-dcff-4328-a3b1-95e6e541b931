-- ===================================
-- إنشاء جداول تتبع المستخدمين المتصلين (نسخة محدثة)
-- لتطبيق إدارة الكاميرا - Moon Memory
-- ===================================

-- 🔍 فحص الجداول الموجودة أولاً
SELECT 'بدء إنشاء جداول تتبع المستخدمين...' as status;

-- ===================================
-- 1. جدول جلسات المستخدمين
-- ===================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    device_name TEXT,
    device_type TEXT CHECK (device_type IN ('mobile', 'tablet', 'desktop', 'unknown')), 
    app_version TEXT,
    platform TEXT CHECK (platform IN ('android', 'ios', 'windows', 'web', 'unknown')),
    
    -- معلومات الجلسة
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    
    -- معلومات الشبكة والموقع
    ip_address INET,
    location_lat DOUBLE PRECISION,
    location_lng DOUBLE PRECISION,
    location_name TEXT,
    location_accuracy DOUBLE PRECISION,
    
    -- معلومات الجهاز
    battery_level INTEGER CHECK (battery_level >= 0 AND battery_level <= 100),
    storage_used_mb BIGINT DEFAULT 0,
    storage_available_mb BIGINT DEFAULT 0,
    
    -- معلومات إضافية
    user_agent TEXT,
    network_type TEXT,
    connection_quality TEXT CHECK (connection_quality IN ('excellent', 'good', 'fair', 'poor', 'unknown')),
    
    -- طوابع زمنية
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================
-- 2. جدول سجل نشاط المستخدمين
-- ===================================

CREATE TABLE IF NOT EXISTS user_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    
    -- نوع النشاط
    activity_type TEXT NOT NULL CHECK (activity_type IN (
        'login', 'logout', 'heartbeat', 'app_open', 'app_close',
        'photo_taken', 'video_recorded', 'photo_uploaded', 'video_uploaded',
        'location_update', 'settings_changed', 'error_occurred'
    )),
    
    -- بيانات النشاط (JSON مرن)
    activity_data JSONB DEFAULT '{}',
    
    -- معلومات الموقع والوقت
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    location_lat DOUBLE PRECISION,
    location_lng DOUBLE PRECISION,
    location_name TEXT,
    
    -- معلومات إضافية
    duration_seconds INTEGER,
    file_size_bytes BIGINT,
    error_message TEXT,
    
    -- فهرسة للبحث السريع
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================
-- 3. تحديث جدول المستخدمين الموجود
-- ===================================

-- إضافة أعمدة جديدة لتتبع الحالة
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS is_online BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS current_session_id UUID,
ADD COLUMN IF NOT EXISTS total_sessions INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_activity_time_minutes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_ip_address INET,
ADD COLUMN IF NOT EXISTS last_location_lat DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS last_location_lng DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS last_location_name TEXT;

-- إضافة Foreign Key بعد إنشاء الجدول
ALTER TABLE users 
ADD CONSTRAINT fk_users_current_session 
FOREIGN KEY (current_session_id) REFERENCES user_sessions(id) ON DELETE SET NULL;

-- ===================================
-- 4. إنشاء الفهارس للأداء
-- ===================================

-- فهارس جدول user_sessions
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);
CREATE INDEX IF NOT EXISTS idx_user_sessions_device_id ON user_sessions(device_id);

-- فهارس جدول user_activity_log
CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_session_id ON user_activity_log(session_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_activity_type ON user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_activity_log_timestamp ON user_activity_log(timestamp);

-- فهارس جدول users المحدث
CREATE INDEX IF NOT EXISTS idx_users_is_online ON users(is_online);
CREATE INDEX IF NOT EXISTS idx_users_last_seen ON users(last_seen);
CREATE INDEX IF NOT EXISTS idx_users_current_session ON users(current_session_id);

-- ===================================
-- 5. إنشاء دوال مساعدة
-- ===================================

-- دالة لتحديث آخر نشاط
CREATE OR REPLACE FUNCTION update_user_last_activity(
    p_user_id UUID,
    p_session_id UUID DEFAULT NULL
) RETURNS void AS $$
BEGIN
    UPDATE users 
    SET 
        last_seen = NOW(),
        is_online = true,
        current_session_id = COALESCE(p_session_id, current_session_id)
    WHERE id = p_user_id;
    
    IF p_session_id IS NOT NULL THEN
        UPDATE user_sessions 
        SET 
            last_activity = NOW(),
            updated_at = NOW()
        WHERE id = p_session_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على المستخدمين المتصلين
CREATE OR REPLACE FUNCTION get_online_users(
    p_minutes_threshold INTEGER DEFAULT 2
) RETURNS TABLE (
    user_id UUID,
    full_name TEXT,
    email TEXT,
    last_seen TIMESTAMP WITH TIME ZONE,
    session_id UUID,
    device_name TEXT,
    device_type TEXT,
    platform TEXT,
    battery_level INTEGER,
    location_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.full_name,
        u.email,
        u.last_seen,
        s.id,
        s.device_name,
        s.device_type,
        s.platform,
        s.battery_level,
        s.location_name
    FROM users u
    LEFT JOIN user_sessions s ON u.current_session_id = s.id
    WHERE 
        u.is_online = true 
        AND u.last_seen > NOW() - INTERVAL '1 minute' * p_minutes_threshold
        AND u.is_active = true
    ORDER BY u.last_seen DESC;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنهاء الجلسة
CREATE OR REPLACE FUNCTION end_user_session(
    p_session_id UUID
) RETURNS void AS $$
DECLARE
    v_user_id UUID;
BEGIN
    SELECT user_id INTO v_user_id FROM user_sessions WHERE id = p_session_id;

    UPDATE user_sessions
    SET session_end = NOW(), is_active = false, updated_at = NOW()
    WHERE id = p_session_id;

    UPDATE users
    SET is_online = false, current_session_id = NULL
    WHERE id = v_user_id;

    INSERT INTO user_activity_log (user_id, session_id, activity_type)
    VALUES (v_user_id, p_session_id, 'logout');
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 6. إعداد الصلاحيات
-- ===================================

ALTER TABLE user_sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_log DISABLE ROW LEVEL SECURITY;

GRANT ALL PRIVILEGES ON user_sessions TO authenticated;
GRANT ALL PRIVILEGES ON user_sessions TO service_role;
GRANT ALL PRIVILEGES ON user_activity_log TO authenticated;
GRANT ALL PRIVILEGES ON user_activity_log TO service_role;

-- ===================================
-- 7. إنشاء View للمستخدمين المتصلين
-- ===================================

CREATE OR REPLACE VIEW v_online_users AS
SELECT
    u.id, u.national_id, u.full_name, u.email, u.department, u.position, u.last_seen,
    s.device_name, s.device_type, s.platform, s.battery_level, s.location_name, s.last_activity,
    EXTRACT(EPOCH FROM (NOW() - u.last_seen))/60 as minutes_since_last_seen
FROM users u
LEFT JOIN user_sessions s ON u.current_session_id = s.id
WHERE u.is_online = true AND u.is_active = true AND u.last_seen > NOW() - INTERVAL '5 minutes'
ORDER BY u.last_seen DESC;

-- ===================================
-- 8. إدراج بيانات تجريبية
-- ===================================

UPDATE users
SET
    last_seen = CASE
        WHEN is_active = true THEN NOW() - INTERVAL '1 hour' * (RANDOM() * 24)
        ELSE NOW() - INTERVAL '1 day' * (RANDOM() * 7)
    END,
    is_online = CASE
        WHEN is_active = true AND RANDOM() > 0.5 THEN true
        ELSE false
    END,
    total_sessions = FLOOR(RANDOM() * 100) + 1,
    total_activity_time_minutes = FLOOR(RANDOM() * 10000) + 100
WHERE last_seen IS NULL;

-- ===================================
-- 9. فحص النتائج
-- ===================================

SELECT table_name,
    CASE
        WHEN table_name IN ('user_sessions', 'user_activity_log') THEN '✅ جديد'
        WHEN table_name = 'users' THEN '🔄 محدث'
        ELSE '📋 موجود'
    END as status
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'user_sessions', 'user_activity_log')
ORDER BY table_name;

SELECT '👥 المستخدمين المتصلين حالياً:' as info, COUNT(*) as count FROM v_online_users;

SELECT 'إجمالي المستخدمين' as metric, COUNT(*) as value FROM users
UNION ALL
SELECT 'المستخدمين النشطين', COUNT(*) FROM users WHERE is_active = true
UNION ALL
SELECT 'المستخدمين المتصلين', COUNT(*) FROM users WHERE is_online = true;

SELECT '🎉 تم إنشاء جميع جداول تتبع المستخدمين بنجاح!' as status;
