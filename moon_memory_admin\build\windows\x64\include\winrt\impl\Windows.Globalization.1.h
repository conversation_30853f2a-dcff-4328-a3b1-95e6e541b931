// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Globalization_1_H
#define WINRT_Windows_Globalization_1_H
#include "winrt/impl/Windows.Globalization.0.h"
WINRT_EXPORT namespace winrt::Windows::Globalization
{
    struct __declspec(empty_bases) IApplicationLanguagesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationLanguagesStatics>
    {
        IApplicationLanguagesStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationLanguagesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IApplicationLanguagesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationLanguagesStatics2>
    {
        IApplicationLanguagesStatics2(std::nullptr_t = nullptr) noexcept {}
        IApplicationLanguagesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendar>
    {
        ICalendar(std::nullptr_t = nullptr) noexcept {}
        ICalendar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarFactory>
    {
        ICalendarFactory(std::nullptr_t = nullptr) noexcept {}
        ICalendarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarFactory2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarFactory2>
    {
        ICalendarFactory2(std::nullptr_t = nullptr) noexcept {}
        ICalendarFactory2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarIdentifiersStatics>
    {
        ICalendarIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ICalendarIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarIdentifiersStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarIdentifiersStatics2>
    {
        ICalendarIdentifiersStatics2(std::nullptr_t = nullptr) noexcept {}
        ICalendarIdentifiersStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarIdentifiersStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarIdentifiersStatics3>
    {
        ICalendarIdentifiersStatics3(std::nullptr_t = nullptr) noexcept {}
        ICalendarIdentifiersStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IClockIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IClockIdentifiersStatics>
    {
        IClockIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IClockIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICurrencyAmount :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICurrencyAmount>
    {
        ICurrencyAmount(std::nullptr_t = nullptr) noexcept {}
        ICurrencyAmount(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICurrencyAmountFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICurrencyAmountFactory>
    {
        ICurrencyAmountFactory(std::nullptr_t = nullptr) noexcept {}
        ICurrencyAmountFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICurrencyIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICurrencyIdentifiersStatics>
    {
        ICurrencyIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ICurrencyIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICurrencyIdentifiersStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICurrencyIdentifiersStatics2>
    {
        ICurrencyIdentifiersStatics2(std::nullptr_t = nullptr) noexcept {}
        ICurrencyIdentifiersStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICurrencyIdentifiersStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICurrencyIdentifiersStatics3>
    {
        ICurrencyIdentifiersStatics3(std::nullptr_t = nullptr) noexcept {}
        ICurrencyIdentifiersStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeographicRegion :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeographicRegion>
    {
        IGeographicRegion(std::nullptr_t = nullptr) noexcept {}
        IGeographicRegion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeographicRegionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeographicRegionFactory>
    {
        IGeographicRegionFactory(std::nullptr_t = nullptr) noexcept {}
        IGeographicRegionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeographicRegionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeographicRegionStatics>
    {
        IGeographicRegionStatics(std::nullptr_t = nullptr) noexcept {}
        IGeographicRegionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguage>
    {
        ILanguage(std::nullptr_t = nullptr) noexcept {}
        ILanguage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguage2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguage2>
    {
        ILanguage2(std::nullptr_t = nullptr) noexcept {}
        ILanguage2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguage3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguage3>
    {
        ILanguage3(std::nullptr_t = nullptr) noexcept {}
        ILanguage3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguageExtensionSubtags :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguageExtensionSubtags>
    {
        ILanguageExtensionSubtags(std::nullptr_t = nullptr) noexcept {}
        ILanguageExtensionSubtags(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguageFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguageFactory>
    {
        ILanguageFactory(std::nullptr_t = nullptr) noexcept {}
        ILanguageFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguageStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguageStatics>
    {
        ILanguageStatics(std::nullptr_t = nullptr) noexcept {}
        ILanguageStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguageStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguageStatics2>
    {
        ILanguageStatics2(std::nullptr_t = nullptr) noexcept {}
        ILanguageStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILanguageStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILanguageStatics3>
    {
        ILanguageStatics3(std::nullptr_t = nullptr) noexcept {}
        ILanguageStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INumeralSystemIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INumeralSystemIdentifiersStatics>
    {
        INumeralSystemIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        INumeralSystemIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INumeralSystemIdentifiersStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INumeralSystemIdentifiersStatics2>
    {
        INumeralSystemIdentifiersStatics2(std::nullptr_t = nullptr) noexcept {}
        INumeralSystemIdentifiersStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimeZoneOnCalendar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimeZoneOnCalendar>
    {
        ITimeZoneOnCalendar(std::nullptr_t = nullptr) noexcept {}
        ITimeZoneOnCalendar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
