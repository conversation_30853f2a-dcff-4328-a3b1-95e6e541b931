import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/devices_provider.dart';
import '../../models/device_model.dart';
import 'edit_device_dialog.dart';

class DevicesDataTable extends ConsumerWidget {
  const DevicesDataTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final devicesState = ref.watch(devicesProvider);

    return Column(
      children: [
        // جدول الأجهزة
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: MediaQuery.of(context).size.width - 48,
              child: DataTable(
                headingRowColor: WidgetStateProperty.all(
                  AppColors.primaryGold.withValues(alpha: 0.1),
                ),
                dataRowColor: WidgetStateProperty.resolveWith((states) {
                  if (states.contains(WidgetState.hovered)) {
                    return AppColors.primaryGold.withValues(alpha: 0.05);
                  }
                  return AppColors.surfaceBackground;
                }),
                headingTextStyle: const TextStyle(
                  color: AppColors.primaryGold,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                dataTextStyle: const TextStyle(
                  color: AppColors.primaryText,
                  fontSize: 13,
                ),
                columnSpacing: 20,
                horizontalMargin: 16,
                columns: const [
                  DataColumn(
                    label: Text('معرف الجهاز'),
                    tooltip: 'معرف الجهاز الفريد',
                  ),
                  DataColumn(
                    label: Text('اسم الجهاز'),
                    tooltip: 'اسم الجهاز',
                  ),
                  DataColumn(
                    label: Text('النوع'),
                    tooltip: 'نوع الجهاز',
                  ),
                  DataColumn(
                    label: Text('الموديل'),
                    tooltip: 'موديل الجهاز',
                  ),
                  DataColumn(
                    label: Text('المستخدم'),
                    tooltip: 'المستخدم المسؤول عن الجهاز',
                  ),
                  DataColumn(
                    label: Text('الموقع'),
                    tooltip: 'موقع الجهاز',
                  ),
                  DataColumn(
                    label: Text('الحالة'),
                    tooltip: 'حالة الجهاز',
                  ),
                  DataColumn(
                    label: Text('تاريخ الإنشاء'),
                    tooltip: 'تاريخ إضافة الجهاز',
                  ),
                  DataColumn(
                    label: Text('الإجراءات'),
                    tooltip: 'إجراءات الجهاز',
                  ),
                ],
                rows: devicesState.devices.map((device) => _buildDataRow(context, ref, device)).toList(),
              ),
            ),
          ),
        ),

        // شريط التنقل بين الصفحات
        if (devicesState.totalPages > 1) _buildPaginationBar(context, ref, devicesState),
      ],
    );
  }

  DataRow _buildDataRow(BuildContext context, WidgetRef ref, DeviceModel device) {
    return DataRow(
      cells: [
        // معرف الجهاز
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              device.deviceId,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
                color: AppColors.info,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        // اسم الجهاز
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                device.deviceName,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              if (device.description?.isNotEmpty == true)
                Text(
                  device.description!,
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 11,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),

        // النوع
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getDeviceTypeIcon(device.deviceType),
                size: 16,
                color: AppColors.primaryGold,
              ),
              const SizedBox(width: 6),
              Text(device.deviceType),
            ],
          ),
        ),

        // الموديل
        DataCell(Text(device.model)),

        // المستخدم
        DataCell(
          device.user != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      device.user!.fullName ?? 'غير محدد',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    if (device.user!.nationalId?.isNotEmpty == true)
                      Text(
                        device.user!.nationalId!,
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 11,
                        ),
                      ),
                  ],
                )
              : Text(
                  'غير مخصص',
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontStyle: FontStyle.italic,
                  ),
                ),
        ),

        // الموقع
        DataCell(
          device.location != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      device.location!.locationNameAr ?? device.location!.locationCode,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Text(
                      'كود: ${device.location!.locationCode}',
                      style: TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 11,
                      ),
                    ),
                  ],
                )
              : Text(
                  'غير محدد',
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontStyle: FontStyle.italic,
                  ),
                ),
        ),

        // الحالة
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(device.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getStatusColor(device.status),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: _getStatusColor(device.status),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  _getStatusText(device.status),
                  style: TextStyle(
                    color: _getStatusColor(device.status),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),

        // تاريخ الإنشاء
        DataCell(
          device.createdAt != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _formatDate(device.createdAt!),
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      _formatTime(device.createdAt!),
                      style: TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 10,
                      ),
                    ),
                  ],
                )
              : Text(
                  'غير محدد',
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontStyle: FontStyle.italic,
                  ),
                ),
        ),

        // الإجراءات
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر التعديل
              IconButton(
                onPressed: () => _showEditDeviceDialog(context, device),
                icon: const Icon(Icons.edit, size: 18),
                color: AppColors.info,
                tooltip: 'تعديل الجهاز',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),

              // زر تغيير الحالة
              IconButton(
                onPressed: () => _toggleDeviceStatus(context, ref, device),
                icon: Icon(
                  device.status == 'active' ? Icons.pause_circle : Icons.play_circle,
                  size: 18,
                ),
                color: device.status == 'active' ? AppColors.warning : AppColors.success,
                tooltip: device.status == 'active' ? 'إيقاف الجهاز' : 'تفعيل الجهاز',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),

              // زر الحذف
              IconButton(
                onPressed: () => _showDeleteConfirmation(context, ref, device),
                icon: const Icon(Icons.delete, size: 18),
                color: AppColors.error,
                tooltip: 'حذف الجهاز',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaginationBar(BuildContext context, WidgetRef ref, DevicesState devicesState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Text(
            'عرض ${devicesState.devices.length} من أصل ${devicesState.totalCount} جهاز',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 14,
            ),
          ),
          const Spacer(),

          // أزرار التنقل
          Row(
            children: [
              IconButton(
                onPressed: devicesState.currentPage > 1
                    ? () => ref.read(devicesProvider.notifier).loadDevices(page: 1, reset: true)
                    : null,
                icon: const Icon(Icons.first_page),
                tooltip: 'الصفحة الأولى',
              ),
              IconButton(
                onPressed: devicesState.currentPage > 1
                    ? () => ref.read(devicesProvider.notifier).loadDevices(
                          page: devicesState.currentPage - 1,
                          reset: true,
                        )
                    : null,
                icon: const Icon(Icons.chevron_left),
                tooltip: 'الصفحة السابقة',
              ),

              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.primaryGold.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${devicesState.currentPage} من ${devicesState.totalPages}',
                  style: const TextStyle(
                    color: AppColors.primaryGold,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              IconButton(
                onPressed: devicesState.currentPage < devicesState.totalPages
                    ? () => ref.read(devicesProvider.notifier).loadDevices(
                          page: devicesState.currentPage + 1,
                          reset: true,
                        )
                    : null,
                icon: const Icon(Icons.chevron_right),
                tooltip: 'الصفحة التالية',
              ),
              IconButton(
                onPressed: devicesState.currentPage < devicesState.totalPages
                    ? () => ref.read(devicesProvider.notifier).loadDevices(
                          page: devicesState.totalPages,
                          reset: true,
                        )
                    : null,
                icon: const Icon(Icons.last_page),
                tooltip: 'الصفحة الأخيرة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getDeviceTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'camera':
      case 'كاميرا':
        return Icons.camera_alt;
      case 'sensor':
      case 'مستشعر':
        return Icons.sensors;
      case 'recorder':
      case 'مسجل':
        return Icons.mic;
      case 'tracker':
      case 'متتبع':
        return Icons.gps_fixed;
      default:
        return Icons.device_unknown;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return AppColors.success;
      case 'inactive':
        return AppColors.warning;
      case 'maintenance':
        return AppColors.info;
      default:
        return AppColors.secondaryText;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'متوقف';
      case 'maintenance':
        return 'صيانة';
      default:
        return status;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showEditDeviceDialog(BuildContext context, DeviceModel device) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => EditDeviceDialog(device: device),
    );
  }

  void _toggleDeviceStatus(BuildContext context, WidgetRef ref, DeviceModel device) {
    final action = device.status == 'active' ? 'إيقاف' : 'تفعيل';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$action الجهاز'),
        content: Text('هل أنت متأكد من $action الجهاز "${device.deviceName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await ref.read(devicesProvider.notifier).toggleDeviceStatus(device.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم $action الجهاز بنجاح'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (error) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل في $action الجهاز: ${error.toString()}'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: device.status == 'active' ? AppColors.warning : AppColors.success,
            ),
            child: Text(action),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, DeviceModel device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الجهاز'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف الجهاز "${device.deviceName}"؟'),
            const SizedBox(height: 8),
            const Text(
              'تحذير: سيتم حذف جميع البيانات المرتبطة بهذا الجهاز نهائياً.',
              style: TextStyle(
                color: AppColors.error,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await ref.read(devicesProvider.notifier).deleteDevice(device.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف الجهاز بنجاح'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (error) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل في حذف الجهاز: ${error.toString()}'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
