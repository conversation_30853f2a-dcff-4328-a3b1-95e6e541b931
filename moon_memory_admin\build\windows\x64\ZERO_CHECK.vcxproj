﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C5825266-62BB-3A53-A41E-0C81E34DD81F}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\1d9de88832ee210bf7aa3e54ef25391e\generate.stamp.rule">
      <StdOutEncoding>UTF-8</StdOutEncoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/moon_memory_admin.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeCXXInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeGenericSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeInitializeConfigs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeLanguageInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeRCInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FetchContent.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows-MSVC.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeCXXCompiler.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeRCCompiler.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeSystem.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\generated_config.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\generated_plugins.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\flutter\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\app_links\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\geolocator_windows\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\url_launcher_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/moon_memory_admin.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeCXXInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeGenericSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeInitializeConfigs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeLanguageInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeRCInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FetchContent.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows-MSVC.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeCXXCompiler.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeRCCompiler.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeSystem.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\generated_config.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\generated_plugins.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\flutter\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\app_links\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\geolocator_windows\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\url_launcher_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/moon_memory_admin.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeCXXInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeGenericSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeInitializeConfigs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeLanguageInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeRCInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FetchContent.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows-MSVC.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeCXXCompiler.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeRCCompiler.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CMakeSystem.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\CMakeLists.txt;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\ephemeral\generated_config.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\flutter\generated_plugins.cmake;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\windows\runner\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\flutter\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\runner\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\app_links\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\geolocator_windows\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\permission_handler_windows\CMakeFiles\generate.stamp;C:\Users\<USER>\CascadeProjects\moon_memory_v2\moon_memory_admin\moon_memory_admin\build\windows\x64\plugins\url_launcher_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>