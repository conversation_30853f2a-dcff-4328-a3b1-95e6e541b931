// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Security_Isolation_1_H
#define WINRT_Windows_Security_Isolation_1_H
#include "winrt/impl/Windows.Security.Isolation.0.h"
WINRT_EXPORT namespace winrt::Windows::Security::Isolation
{
    struct __declspec(empty_bases) IIsolatedWindowsEnvironment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironment>
    {
        IIsolatedWindowsEnvironment(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironment2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironment2>,
        impl::require<winrt::Windows::Security::Isolation::IIsolatedWindowsEnvironment2, winrt::Windows::Security::Isolation::IIsolatedWindowsEnvironment>
    {
        IIsolatedWindowsEnvironment2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironment2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentCreateResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentCreateResult>
    {
        IIsolatedWindowsEnvironmentCreateResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentCreateResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentFactory>
    {
        IIsolatedWindowsEnvironmentFactory(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentFile :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentFile>
    {
        IIsolatedWindowsEnvironmentFile(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentFile(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentHostStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentHostStatics>
    {
        IIsolatedWindowsEnvironmentHostStatics(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentHostStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentLaunchFileResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentLaunchFileResult>
    {
        IIsolatedWindowsEnvironmentLaunchFileResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentLaunchFileResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOptions>
    {
        IIsolatedWindowsEnvironmentOptions(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentOwnerRegistrationData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOwnerRegistrationData>
    {
        IIsolatedWindowsEnvironmentOwnerRegistrationData(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOwnerRegistrationData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentOwnerRegistrationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOwnerRegistrationResult>
    {
        IIsolatedWindowsEnvironmentOwnerRegistrationResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOwnerRegistrationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentOwnerRegistrationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOwnerRegistrationStatics>
    {
        IIsolatedWindowsEnvironmentOwnerRegistrationStatics(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOwnerRegistrationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentPostMessageResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentPostMessageResult>
    {
        IIsolatedWindowsEnvironmentPostMessageResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentPostMessageResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentProcess :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentProcess>
    {
        IIsolatedWindowsEnvironmentProcess(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentProcess(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentShareFolderRequestOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentShareFolderRequestOptions>
    {
        IIsolatedWindowsEnvironmentShareFolderRequestOptions(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentShareFolderRequestOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentShareFolderResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentShareFolderResult>
    {
        IIsolatedWindowsEnvironmentShareFolderResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentShareFolderResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentStartProcessResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentStartProcessResult>
    {
        IIsolatedWindowsEnvironmentStartProcessResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentStartProcessResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsEnvironmentTelemetryParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentTelemetryParameters>
    {
        IIsolatedWindowsEnvironmentTelemetryParameters(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentTelemetryParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsHostMessengerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsHostMessengerStatics>
    {
        IIsolatedWindowsHostMessengerStatics(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsHostMessengerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsolatedWindowsHostMessengerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsHostMessengerStatics2>
    {
        IIsolatedWindowsHostMessengerStatics2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsHostMessengerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
