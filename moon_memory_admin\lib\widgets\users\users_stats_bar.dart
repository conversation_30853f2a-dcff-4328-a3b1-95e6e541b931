import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/users_provider.dart';
import '../../models/user_model.dart';

class UsersStatsBar extends ConsumerWidget {
  const UsersStatsBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersState = ref.watch(usersProvider);
    final activeUsersCount = ref.watch(activeUsersCountProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        children: [
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'إجمالي المستخدمين',
              value: usersState.totalCount.toString(),
              icon: Icons.people,
              color: AppColors.primaryGold,
              onTap: () => _showAllUsersDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'المستخدمين النشطين',
              value: activeUsersCount.toString(),
              icon: Icons.person,
              color: AppColors.success,
              onTap: () => _showActiveUsersDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'المتصلين الآن',
              value: activeUsersCount.toString(),
              icon: Icons.people,
              color: AppColors.success,
              onTap: () => _showOnlineUsersDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'المستخدمين غير النشطين',
              value: (usersState.totalCount - activeUsersCount).toString(),
              icon: Icons.person_off,
              color: AppColors.warning,
              onTap: () => _showInactiveUsersDialog(context, ref),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableStatCard(
    BuildContext context,
    WidgetRef ref, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.visibility,
                    color: AppColors.secondaryText,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.secondaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دوال عرض النوافذ المنبثقة للإحصائيات
  void _showAllUsersDialog(BuildContext context, WidgetRef ref) {
    final usersState = ref.read(usersProvider);
    final allUsers = usersState.users;

    _showUsersDialog(
      context: context,
      title: 'جميع المستخدمين',
      users: allUsers,
      color: AppColors.primaryGold,
      icon: Icons.people,
    );
  }

  void _showActiveUsersDialog(BuildContext context, WidgetRef ref) {
    final usersState = ref.read(usersProvider);
    final activeUsers = usersState.users.where((u) => u.isActive).toList();

    _showUsersDialog(
      context: context,
      title: 'المستخدمين النشطين',
      users: activeUsers,
      color: AppColors.success,
      icon: Icons.person,
    );
  }

  void _showInactiveUsersDialog(BuildContext context, WidgetRef ref) {
    final usersState = ref.read(usersProvider);
    final inactiveUsers = usersState.users.where((u) => !u.isActive).toList();

    _showUsersDialog(
      context: context,
      title: 'المستخدمين غير النشطين',
      users: inactiveUsers,
      color: AppColors.warning,
      icon: Icons.person_off,
    );
  }

  void _showUsersDialog(
      {required BuildContext context,
      required String title,
      required List<UserModel> users,
      required Color color,
      required IconData icon}) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(icon, color: color, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${users.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: AppColors.secondaryText,
                    ),
                  ],
                ),
              ),

              // محتوى النافذة
              Flexible(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: users.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                icon,
                                size: 64,
                                color: AppColors.secondaryText,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا يوجد مستخدمين في هذه القائمة',
                                style: TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: users.length,
                          itemBuilder: (context, index) {
                            final user = users[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceBackground,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: color.withValues(alpha: 0.2),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Stack(
                                    children: [
                                      CircleAvatar(
                                        backgroundColor: user.isAdmin
                                            ? AppColors.primaryGold
                                            : AppColors.info,
                                        radius: 24,
                                        child: Text(
                                          user.fullName?.substring(0, 1) ?? 'م',
                                          style: const TextStyle(
                                            color: AppColors.darkBackground,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                      if (user.isActive)
                                        Positioned(
                                          right: 0,
                                          bottom: 0,
                                          child: Container(
                                            width: 14,
                                            height: 14,
                                            decoration: BoxDecoration(
                                              color: AppColors.success,
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: AppColors.surfaceBackground,
                                                width: 2,
                                              ),
                                            ),
                                          ),
                                        ),
                                      if (user.isAdmin)
                                        Positioned(
                                          left: 0,
                                          top: 0,
                                          child: Container(
                                            width: 18,
                                            height: 18,
                                            decoration: BoxDecoration(
                                              color: AppColors.primaryGold,
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: AppColors.surfaceBackground,
                                                width: 1,
                                              ),
                                            ),
                                            child: Icon(
                                              Icons.star,
                                              size: 12,
                                              color: AppColors.darkBackground,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          user.fullName ?? 'غير محدد',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: AppColors.primaryText,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          user.department ?? 'غير محدد',
                                          style: TextStyle(
                                            color: AppColors.secondaryText,
                                            fontSize: 14,
                                          ),
                                        ),
                                        if (user.position?.isNotEmpty == true) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            user.position!,
                                            style: TextStyle(
                                              color: AppColors.secondaryText,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: user.isAdmin
                                          ? AppColors.primaryGold.withValues(alpha: 0.1)
                                          : AppColors.info.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: user.isAdmin
                                            ? AppColors.primaryGold
                                            : AppColors.info,
                                      ),
                                    ),
                                    child: Text(
                                      user.isAdmin ? 'مدير' : 'مستخدم',
                                      style: TextStyle(
                                        color: user.isAdmin
                                            ? AppColors.primaryGold
                                            : AppColors.info,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ),

              // أسفل النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceBackground,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showOnlineUsersDialog(BuildContext context, WidgetRef ref) {
    final usersState = ref.read(usersProvider);
    final onlineUsers = usersState.users.where((u) => u.isActive).toList();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.people, color: AppColors.success, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'المستخدمين المتصلين الآن',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.success,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${onlineUsers.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: AppColors.secondaryText,
                    ),
                  ],
                ),
              ),

              // محتوى النافذة
              Flexible(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: onlineUsers.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.people_outline,
                                size: 64,
                                color: AppColors.secondaryText,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا يوجد مستخدمين متصلين حالياً',
                                style: TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: onlineUsers.length,
                          itemBuilder: (context, index) {
                            final user = onlineUsers[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceBackground,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.success.withValues(alpha: 0.2),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Stack(
                                    children: [
                                      CircleAvatar(
                                        backgroundColor: user.isAdmin
                                            ? AppColors.primaryGold
                                            : AppColors.info,
                                        radius: 24,
                                        child: Text(
                                          user.fullName?.substring(0, 1) ?? 'م',
                                          style: const TextStyle(
                                            color: AppColors.darkBackground,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        right: 0,
                                        bottom: 0,
                                        child: Container(
                                          width: 14,
                                          height: 14,
                                          decoration: BoxDecoration(
                                            color: AppColors.success,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: AppColors.surfaceBackground,
                                              width: 2,
                                            ),
                                          ),
                                        ),
                                      ),
                                      if (user.isAdmin)
                                        Positioned(
                                          left: 0,
                                          top: 0,
                                          child: Container(
                                            width: 18,
                                            height: 18,
                                            decoration: BoxDecoration(
                                              color: AppColors.primaryGold,
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: AppColors.surfaceBackground,
                                                width: 1,
                                              ),
                                            ),
                                            child: Icon(
                                              Icons.star,
                                              size: 12,
                                              color: AppColors.darkBackground,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          user.fullName ?? 'غير محدد',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: AppColors.primaryText,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          user.department ?? 'غير محدد',
                                          style: TextStyle(
                                            color: AppColors.secondaryText,
                                            fontSize: 14,
                                          ),
                                        ),
                                        if (user.position?.isNotEmpty == true) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            user.position!,
                                            style: TextStyle(
                                              color: AppColors.secondaryText,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: user.isAdmin
                                          ? AppColors.primaryGold.withValues(alpha: 0.1)
                                          : AppColors.info.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: user.isAdmin
                                            ? AppColors.primaryGold
                                            : AppColors.info,
                                      ),
                                    ),
                                    child: Text(
                                      user.isAdmin ? 'مدير' : 'مستخدم',
                                      style: TextStyle(
                                        color: user.isAdmin
                                            ? AppColors.primaryGold
                                            : AppColors.info,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ),

              // أسفل النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceBackground,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
