import 'user_model.dart';
import 'device_model.dart';
import 'location_model.dart';

class VideoModel {
  final String id;
  final String userId;
  final String fileName;
  final String? storagePath;
  final String videoUrl;
  final String? url;
  final int? fileSizeBytes;
  final int? durationSeconds;
  final String location;
  final String locationType;
  final String? locationNumber;
  final String? fullLocationCode;
  final String username;
  final DateTime captureTimestamp;
  final DateTime uploadTimestamp;
  final int? sortOrder;
  final List<String>? tags;
  final String? description;
  final String? resolution;
  final int? fps;
  final String? codec;
  final int? bitrate;
  final Map<String, dynamic>? cameraSettings;
  final Map<String, dynamic>? gpsCoordinates;
  final Map<String, dynamic>? weatherInfo;
  final String status;
  final bool isProcessed;
  final Map<String, dynamic>? processingInfo;
  final String? thumbnailUrl;
  final DateTime dateTime;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // العلاقات
  final UserModel? user;
  final DeviceModel? device;
  final LocationModel? locationModel;

  const VideoModel({
    required this.id,
    required this.userId,
    required this.fileName,
    this.storagePath,
    required this.videoUrl,
    this.url,
    this.fileSizeBytes,
    this.durationSeconds,
    required this.location,
    required this.locationType,
    this.locationNumber,
    this.fullLocationCode,
    required this.username,
    required this.captureTimestamp,
    required this.uploadTimestamp,
    this.sortOrder,
    this.tags,
    this.description,
    this.resolution,
    this.fps,
    this.codec,
    this.bitrate,
    this.cameraSettings,
    this.gpsCoordinates,
    this.weatherInfo,
    this.status = 'active',
    this.isProcessed = false,
    this.processingInfo,
    this.thumbnailUrl,
    required this.dateTime,
    this.createdAt,
    this.updatedAt,
    this.user,
    this.device,
    this.locationModel,
  });

  factory VideoModel.fromJson(Map<String, dynamic> json) {
    return VideoModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      fileName: json['file_name'] as String,
      storagePath: json['storage_path'] as String?,
      videoUrl: json['video_url'] as String,
      url: json['url'] as String?,
      fileSizeBytes: json['file_size_bytes'] as int?,
      durationSeconds: json['duration_seconds'] as int?,
      location: json['location'] as String,
      locationType: json['location_type'] as String,
      locationNumber: json['location_number'] as String?,
      fullLocationCode: json['full_location_code'] as String?,
      username: json['username'] as String,
      captureTimestamp: DateTime.parse(json['capture_timestamp'] as String),
      uploadTimestamp: DateTime.parse(json['upload_timestamp'] as String),
      sortOrder: json['sort_order'] as int?,
      tags: json['tags'] != null 
          ? List<String>.from(json['tags'] as List) 
          : null,
      description: json['description'] as String?,
      resolution: json['resolution'] as String?,
      fps: json['fps'] as int?,
      codec: json['codec'] as String?,
      bitrate: json['bitrate'] as int?,
      cameraSettings: json['camera_settings'] as Map<String, dynamic>?,
      gpsCoordinates: json['gps_coordinates'] as Map<String, dynamic>?,
      weatherInfo: json['weather_info'] as Map<String, dynamic>?,
      status: json['status'] as String? ?? 'active',
      isProcessed: json['is_processed'] as bool? ?? false,
      processingInfo: json['processing_info'] as Map<String, dynamic>?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      dateTime: DateTime.parse(json['date_time'] as String),
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'file_name': fileName,
      'storage_path': storagePath,
      'video_url': videoUrl,
      'url': url,
      'file_size_bytes': fileSizeBytes,
      'duration_seconds': durationSeconds,
      'location': location,
      'location_type': locationType,
      'location_number': locationNumber,
      'full_location_code': fullLocationCode,
      'username': username,
      'capture_timestamp': captureTimestamp.toIso8601String(),
      'upload_timestamp': uploadTimestamp.toIso8601String(),
      'sort_order': sortOrder,
      'tags': tags,
      'description': description,
      'resolution': resolution,
      'fps': fps,
      'codec': codec,
      'bitrate': bitrate,
      'camera_settings': cameraSettings,
      'gps_coordinates': gpsCoordinates,
      'weather_info': weatherInfo,
      'status': status,
      'is_processed': isProcessed,
      'processing_info': processingInfo,
      'thumbnail_url': thumbnailUrl,
      'date_time': dateTime.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Helper getters
  bool get isULocation => locationType == 'U';
  bool get isCLocation => locationType == 'C';
  String get locationTypeArabic => isULocation ? 'جامعة' : 'كلية';
  
  String get fileSizeFormatted {
    if (fileSizeBytes == null) return 'غير محدد';
    final kb = fileSizeBytes! / 1024;
    if (kb < 1024) return '${kb.toStringAsFixed(1)} KB';
    final mb = kb / 1024;
    if (mb < 1024) return '${mb.toStringAsFixed(1)} MB';
    final gb = mb / 1024;
    return '${gb.toStringAsFixed(1)} GB';
  }

  String get durationFormatted {
    if (durationSeconds == null) return 'غير محدد';
    final minutes = durationSeconds! ~/ 60;
    final seconds = durationSeconds! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  String toString() {
    return 'VideoModel(id: $id, fileName: $fileName, fullLocationCode: $fullLocationCode, username: $username)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
