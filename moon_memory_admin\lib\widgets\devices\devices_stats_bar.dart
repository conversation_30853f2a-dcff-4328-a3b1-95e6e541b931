import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/devices_provider.dart';
import '../../models/device_model.dart';

class DevicesStatsBar extends ConsumerWidget {
  const DevicesStatsBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final devicesState = ref.watch(devicesProvider);
    final activeDevicesCount = ref.watch(activeDevicesCountProvider);
    final inactiveDevicesCount = ref.watch(inactiveDevicesCountProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        children: [
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'إجمالي الأجهزة',
              value: devicesState.totalCount.toString(),
              icon: Icons.devices,
              color: AppColors.primaryGold,
              onTap: () => _showAllDevicesDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'الأجهزة النشطة',
              value: activeDevicesCount.toString(),
              icon: Icons.check_circle,
              color: AppColors.success,
              onTap: () => _showActiveDevicesDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'الأجهزة المتوقفة',
              value: inactiveDevicesCount.toString(),
              icon: Icons.cancel,
              color: AppColors.warning,
              onTap: () => _showInactiveDevicesDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'أنواع الأجهزة',
              value: _getDeviceTypesCount(devicesState.devices).toString(),
              icon: Icons.category,
              color: AppColors.info,
              onTap: () => _showDeviceTypesDialog(context, ref),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableStatCard(
    BuildContext context,
    WidgetRef ref, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.visibility,
                    color: AppColors.secondaryText,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.secondaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getDeviceTypesCount(List<DeviceModel> devices) {
    final types = devices.map((device) => device.deviceType).toSet();
    return types.length;
  }

  // دوال عرض النوافذ المنبثقة للإحصائيات
  void _showAllDevicesDialog(BuildContext context, WidgetRef ref) {
    final devicesState = ref.read(devicesProvider);
    final allDevices = devicesState.devices;

    _showDevicesDialog(
      context: context,
      title: 'جميع الأجهزة',
      devices: allDevices,
      color: AppColors.primaryGold,
      icon: Icons.devices,
    );
  }

  void _showActiveDevicesDialog(BuildContext context, WidgetRef ref) {
    final devicesState = ref.read(devicesProvider);
    final activeDevices = devicesState.devices.where((d) => d.status == 'active').toList();

    _showDevicesDialog(
      context: context,
      title: 'الأجهزة النشطة',
      devices: activeDevices,
      color: AppColors.success,
      icon: Icons.check_circle,
    );
  }

  void _showInactiveDevicesDialog(BuildContext context, WidgetRef ref) {
    final devicesState = ref.read(devicesProvider);
    final inactiveDevices = devicesState.devices.where((d) => d.status == 'inactive').toList();

    _showDevicesDialog(
      context: context,
      title: 'الأجهزة المتوقفة',
      devices: inactiveDevices,
      color: AppColors.warning,
      icon: Icons.cancel,
    );
  }

  void _showDeviceTypesDialog(BuildContext context, WidgetRef ref) {
    final devicesState = ref.read(devicesProvider);
    final devicesByType = <String, List<DeviceModel>>{};
    
    for (final device in devicesState.devices) {
      devicesByType.putIfAbsent(device.deviceType, () => []).add(device);
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.category, color: AppColors.info, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'أنواع الأجهزة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.info,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${devicesByType.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: AppColors.secondaryText,
                    ),
                  ],
                ),
              ),
              
              // محتوى النافذة
              Flexible(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: devicesByType.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.category,
                                size: 64,
                                color: AppColors.secondaryText,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد أنواع أجهزة',
                                style: TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: devicesByType.length,
                          itemBuilder: (context, index) {
                            final type = devicesByType.keys.elementAt(index);
                            final devices = devicesByType[type]!;
                            
                            return Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceBackground,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.info.withValues(alpha: 0.2),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: AppColors.info.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      _getDeviceTypeIcon(type),
                                      color: AppColors.info,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          type,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: AppColors.primaryText,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          '${devices.length} جهاز',
                                          style: TextStyle(
                                            color: AppColors.secondaryText,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12, 
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppColors.info.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '${devices.length}',
                                      style: TextStyle(
                                        color: AppColors.info,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ),
              
              // أسفل النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceBackground,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDevicesDialog({
    required BuildContext context,
    required String title,
    required List<DeviceModel> devices,
    required Color color,
    required IconData icon,
  }) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          constraints: const BoxConstraints(maxHeight: 700),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(icon, color: color, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${devices.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: AppColors.secondaryText,
                    ),
                  ],
                ),
              ),

              // محتوى النافذة
              Flexible(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: devices.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                icon,
                                size: 64,
                                color: AppColors.secondaryText,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد أجهزة في هذه القائمة',
                                style: TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: devices.length,
                          itemBuilder: (context, index) {
                            final device = devices[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceBackground,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: color.withValues(alpha: 0.2),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: device.status == 'active'
                                          ? AppColors.success.withValues(alpha: 0.1)
                                          : AppColors.warning.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      _getDeviceTypeIcon(device.deviceType),
                                      color: device.status == 'active'
                                          ? AppColors.success
                                          : AppColors.warning,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          device.deviceName,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: AppColors.primaryText,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'المعرف: ${device.deviceId}',
                                          style: TextStyle(
                                            color: AppColors.secondaryText,
                                            fontSize: 12,
                                          ),
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                          'النوع: ${device.deviceType}',
                                          style: TextStyle(
                                            color: AppColors.secondaryText,
                                            fontSize: 12,
                                          ),
                                        ),
                                        if (device.user?.fullName != null) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            'المستخدم: ${device.user!.fullName}',
                                            style: TextStyle(
                                              color: AppColors.secondaryText,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: device.status == 'active'
                                          ? AppColors.success.withValues(alpha: 0.1)
                                          : AppColors.warning.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: device.status == 'active'
                                            ? AppColors.success
                                            : AppColors.warning,
                                      ),
                                    ),
                                    child: Text(
                                      device.status == 'active' ? 'نشط' : 'متوقف',
                                      style: TextStyle(
                                        color: device.status == 'active'
                                            ? AppColors.success
                                            : AppColors.warning,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ),

              // أسفل النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceBackground,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getDeviceTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'camera':
      case 'كاميرا':
        return Icons.camera_alt;
      case 'sensor':
      case 'مستشعر':
        return Icons.sensors;
      case 'recorder':
      case 'مسجل':
        return Icons.mic;
      case 'tracker':
      case 'متتبع':
        return Icons.gps_fixed;
      default:
        return Icons.device_unknown;
    }
  }
}
