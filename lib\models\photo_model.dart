import 'user_model.dart';
import 'device_model.dart';
import 'location_model.dart';

class PhotoModel {
  final String id;
  final String userId;
  final String fileName;
  final String? storagePath;
  final String? imageUrl;
  final String url;
  final int? fileSizeBytes;
  final String location;
  final String locationType;
  final String locationNumber;
  final String fullLocationCode;
  final String username;
  final DateTime captureTimestamp;
  final DateTime uploadTimestamp;
  final int sortOrder;
  final List<String>? tags;
  final String? description;
  final Map<String, dynamic>? cameraSettings;
  final Map<String, dynamic>? gpsCoordinates;
  final Map<String, dynamic>? weatherInfo;
  final String status;
  final bool isProcessed;
  final Map<String, dynamic>? processingInfo;
  final DateTime dateTime;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // العلاقات
  final UserModel? user;
  final DeviceModel? device;
  final LocationModel? locationModel;

  const PhotoModel({
    required this.id,
    required this.userId,
    required this.fileName,
    this.storagePath,
    this.imageUrl,
    required this.url,
    this.fileSizeBytes,
    required this.location,
    required this.locationType,
    required this.locationNumber,
    required this.fullLocationCode,
    required this.username,
    required this.captureTimestamp,
    required this.uploadTimestamp,
    required this.sortOrder,
    this.tags,
    this.description,
    this.cameraSettings,
    this.gpsCoordinates,
    this.weatherInfo,
    this.status = 'active',
    this.isProcessed = false,
    this.processingInfo,
    required this.dateTime,
    this.createdAt,
    this.updatedAt,
    this.user,
    this.device,
    this.locationModel,
  });

  factory PhotoModel.fromJson(Map<String, dynamic> json) {
    return PhotoModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      fileName: json['file_name'] as String,
      storagePath: json['storage_path'] as String?,
      imageUrl: json['image_url'] as String?,
      url: json['url'] as String,
      fileSizeBytes: json['file_size_bytes'] as int?,
      location: json['location'] as String,
      locationType: json['location_type'] as String,
      locationNumber: json['location_number'] as String,
      fullLocationCode: json['full_location_code'] as String,
      username: json['username'] as String,
      captureTimestamp: DateTime.parse(json['capture_timestamp'] as String),
      uploadTimestamp: DateTime.parse(json['upload_timestamp'] as String),
      sortOrder: json['sort_order'] as int,
      tags: json['tags'] != null 
          ? List<String>.from(json['tags'] as List) 
          : null,
      description: json['description'] as String?,
      cameraSettings: json['camera_settings'] as Map<String, dynamic>?,
      gpsCoordinates: json['gps_coordinates'] as Map<String, dynamic>?,
      weatherInfo: json['weather_info'] as Map<String, dynamic>?,
      status: json['status'] as String? ?? 'active',
      isProcessed: json['is_processed'] as bool? ?? false,
      processingInfo: json['processing_info'] as Map<String, dynamic>?,
      dateTime: DateTime.parse(json['date_time'] as String),
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'file_name': fileName,
      'storage_path': storagePath,
      'image_url': imageUrl,
      'url': url,
      'file_size_bytes': fileSizeBytes,
      'location': location,
      'location_type': locationType,
      'location_number': locationNumber,
      'full_location_code': fullLocationCode,
      'username': username,
      'capture_timestamp': captureTimestamp.toIso8601String(),
      'upload_timestamp': uploadTimestamp.toIso8601String(),
      'sort_order': sortOrder,
      'tags': tags,
      'description': description,
      'camera_settings': cameraSettings,
      'gps_coordinates': gpsCoordinates,
      'weather_info': weatherInfo,
      'status': status,
      'is_processed': isProcessed,
      'processing_info': processingInfo,
      'date_time': dateTime.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Helper getters
  bool get isULocation => locationType == 'U';
  bool get isCLocation => locationType == 'C';
  String get locationTypeArabic => isULocation ? 'جامعة' : 'كلية';
  String get fileSizeFormatted {
    if (fileSizeBytes == null) return 'غير محدد';
    final kb = fileSizeBytes! / 1024;
    if (kb < 1024) return '${kb.toStringAsFixed(1)} KB';
    final mb = kb / 1024;
    return '${mb.toStringAsFixed(1)} MB';
  }

  PhotoModel copyWith({
    String? id,
    String? userId,
    String? fileName,
    String? storagePath,
    String? imageUrl,
    String? url,
    int? fileSizeBytes,
    String? location,
    String? locationType,
    String? locationNumber,
    String? fullLocationCode,
    String? username,
    DateTime? captureTimestamp,
    DateTime? uploadTimestamp,
    int? sortOrder,
    List<String>? tags,
    String? description,
    Map<String, dynamic>? cameraSettings,
    Map<String, dynamic>? gpsCoordinates,
    Map<String, dynamic>? weatherInfo,
    String? status,
    bool? isProcessed,
    Map<String, dynamic>? processingInfo,
    DateTime? dateTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PhotoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fileName: fileName ?? this.fileName,
      storagePath: storagePath ?? this.storagePath,
      imageUrl: imageUrl ?? this.imageUrl,
      url: url ?? this.url,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      location: location ?? this.location,
      locationType: locationType ?? this.locationType,
      locationNumber: locationNumber ?? this.locationNumber,
      fullLocationCode: fullLocationCode ?? this.fullLocationCode,
      username: username ?? this.username,
      captureTimestamp: captureTimestamp ?? this.captureTimestamp,
      uploadTimestamp: uploadTimestamp ?? this.uploadTimestamp,
      sortOrder: sortOrder ?? this.sortOrder,
      tags: tags ?? this.tags,
      description: description ?? this.description,
      cameraSettings: cameraSettings ?? this.cameraSettings,
      gpsCoordinates: gpsCoordinates ?? this.gpsCoordinates,
      weatherInfo: weatherInfo ?? this.weatherInfo,
      status: status ?? this.status,
      isProcessed: isProcessed ?? this.isProcessed,
      processingInfo: processingInfo ?? this.processingInfo,
      dateTime: dateTime ?? this.dateTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'PhotoModel(id: $id, fileName: $fileName, fullLocationCode: $fullLocationCode, username: $username)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhotoModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
