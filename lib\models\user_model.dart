class UserModel {
  final String id;
  final String? nationalId;
  final String? fullName;
  final String? email;
  final String? phone;
  final String? passwordHash;
  final bool isActive;
  final bool isAdmin;
  final String? accountType;
  final int? maxDevices;
  final int? storageQuotaMb;
  final String? department;
  final String? position;
  final DateTime? lastLogin;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserModel({
    required this.id,
    this.nationalId,
    this.fullName,
    this.email,
    this.phone,
    this.passwordHash,
    this.isActive = true,
    this.isAdmin = false,
    this.accountType,
    this.maxDevices,
    this.storageQuotaMb,
    this.department,
    this.position,
    this.lastLogin,
    this.createdAt,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      nationalId: json['national_id'] as String?,
      fullName: json['full_name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      passwordHash: json['password_hash'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      isAdmin: json['is_admin'] as bool? ?? false,
      accountType: json['account_type'] as String?,
      maxDevices: json['max_devices'] as int?,
      storageQuotaMb: json['storage_quota_mb'] as int?,
      department: json['department'] as String?,
      position: json['position'] as String?,
      lastLogin: json['last_login'] != null
          ? DateTime.parse(json['last_login'] as String)
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'national_id': nationalId,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'password_hash': passwordHash,
      'is_active': isActive,
      'is_admin': isAdmin,
      'account_type': accountType,
      'max_devices': maxDevices,
      'storage_quota_mb': storageQuotaMb,
      'department': department,
      'position': position,
      'last_login': lastLogin?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? nationalId,
    String? fullName,
    String? email,
    String? phone,
    String? passwordHash,
    bool? isActive,
    bool? isAdmin,
    String? accountType,
    int? maxDevices,
    int? storageQuotaMb,
    String? department,
    String? position,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      nationalId: nationalId ?? this.nationalId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      passwordHash: passwordHash ?? this.passwordHash,
      isActive: isActive ?? this.isActive,
      isAdmin: isAdmin ?? this.isAdmin,
      accountType: accountType ?? this.accountType,
      maxDevices: maxDevices ?? this.maxDevices,
      storageQuotaMb: storageQuotaMb ?? this.storageQuotaMb,
      department: department ?? this.department,
      position: position ?? this.position,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, fullName: $fullName, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
