-- 🛠️ إصلاح الـ Views خطوة بخطوة

-- ===================================
-- الخطوة 1: حذف الـ Views الموجودة
-- ===================================

DROP VIEW IF EXISTS v_simple_online_users CASCADE;
DROP VIEW IF EXISTS v_online_users CASCADE;

SELECT 'تم حذف الـ Views الموجودة' as step_1;

-- ===================================
-- الخطوة 2: إنشاء الجداول المفقودة
-- ===================================

-- إنشاء جدول user_sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    device_id UUID,
    location_name TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول user_activity_log
CREATE TABLE IF NOT EXISTS user_activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    activity_type VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB,
    location_name TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

SELECT 'تم إنشاء الجداول المفقودة' as step_2;

-- ===================================
-- الخطوة 3: إنشاء فهارس
-- ===================================

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);

CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_timestamp ON user_activity_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON user_activity_log(activity_type);

SELECT 'تم إنشاء الفهارس' as step_3;

-- ===================================
-- الخطوة 4: إنشاء View بسيط جديد
-- ===================================

CREATE VIEW v_simple_online_users AS
SELECT
    u.id,
    u.national_id,
    u.full_name,
    u.email,
    u.department,
    u.position,
    u.last_seen,
    u.is_online,
    u.is_active,
    u.current_session_id,
    u.last_location_name,
    
    -- حساب الدقائق منذ آخر ظهور
    CASE 
        WHEN u.last_seen IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (NOW() - u.last_seen))/60 
        ELSE 999 
    END as minutes_since_last_seen,
    
    -- تحديد حالة النص
    CASE
        WHEN u.last_seen IS NULL THEN 'لم يسجل دخول'
        WHEN u.last_seen > NOW() - INTERVAL '2 minutes' THEN 'متصل الآن'
        WHEN u.last_seen > NOW() - INTERVAL '5 minutes' THEN 'نشط مؤخراً'
        WHEN u.last_seen > NOW() - INTERVAL '30 minutes' THEN 'غير نشط'
        ELSE 'غير متصل'
    END as status_text,
    
    -- بيانات الجهاز الأساسية
    d.device_name,
    d.device_model,
    d.device_brand,
    d.last_login as device_last_login
    
FROM users u
LEFT JOIN devices d ON d.user_id = u.id AND d.is_active = true
WHERE u.is_active = true
ORDER BY u.last_seen DESC NULLS LAST;

SELECT 'تم إنشاء v_simple_online_users' as step_4;

-- ===================================
-- الخطوة 5: إنشاء دالة للحصول على عدد المتصلين
-- ===================================

CREATE OR REPLACE FUNCTION get_online_users_count(
    p_minutes_threshold INTEGER DEFAULT 2
) RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM users
        WHERE is_active = true
        AND last_seen IS NOT NULL
        AND last_seen > NOW() - (p_minutes_threshold || ' minutes')::INTERVAL
    );
END;
$$ LANGUAGE plpgsql;

SELECT 'تم إنشاء دالة get_online_users_count' as step_5;

-- ===================================
-- الخطوة 6: اختبار النتائج
-- ===================================

-- عرض الجداول المنشأة
SELECT 'الجداول المنشأة:' as test_tables;
SELECT table_name, 
       CASE 
           WHEN table_name = 'user_sessions' THEN '✅ جلسات المستخدمين'
           WHEN table_name = 'user_activity_log' THEN '✅ سجل الأنشطة'
           ELSE '📋 آخر'
       END as description
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('user_sessions', 'user_activity_log');

-- عرض الـ Views المنشأة
SELECT 'الـ Views المنشأة:' as test_views;
SELECT table_name as view_name,
       '✅ مستخدمين متصلين' as description
FROM information_schema.views
WHERE table_schema = 'public'
AND table_name = 'v_simple_online_users';

-- اختبار الدالة
SELECT 'اختبار الدالة:' as test_function;
SELECT get_online_users_count() as online_users_count;

-- عرض المستخدمين من الـ View الجديد
SELECT 'المستخدمين من v_simple_online_users:' as test_view;
SELECT 
    full_name, 
    email, 
    status_text, 
    ROUND(minutes_since_last_seen::numeric, 1) as minutes_ago,
    device_name
FROM v_simple_online_users
ORDER BY minutes_since_last_seen
LIMIT 5;

-- ===================================
-- الخطوة 7: تحديث بيانات anan للاختبار
-- ===================================

SELECT 'تحديث بيانات anan للاختبار:' as test_update;

-- تحديث آخر ظهور لـ anan إلى الآن
UPDATE users 
SET 
    last_seen = NOW(),
    is_online = true,
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- التحقق من التحديث
SELECT 
    'بعد تحديث anan:' as status,
    full_name,
    email,
    is_online,
    last_seen,
    ROUND(EXTRACT(EPOCH FROM (NOW() - last_seen))/60::numeric, 1) as minutes_since_last_seen
FROM users 
WHERE email = '<EMAIL>';

-- عرض anan من الـ View
SELECT 'anan من v_simple_online_users:' as anan_status;
SELECT 
    full_name,
    email,
    status_text,
    ROUND(minutes_since_last_seen::numeric, 1) as minutes_ago
FROM v_simple_online_users
WHERE email = '<EMAIL>';

-- ===================================
-- الخطوة 8: إحصائيات نهائية
-- ===================================

SELECT 'إحصائيات نهائية:' as final_stats;

SELECT 
    'إجمالي المستخدمين' as metric, 
    COUNT(*) as count 
FROM users
UNION ALL
SELECT 
    'المستخدمين النشطين', 
    COUNT(*) 
FROM users 
WHERE is_active = true
UNION ALL
SELECT 
    'المستخدمين المتصلين (آخر دقيقتين)', 
    get_online_users_count(2)
UNION ALL
SELECT 
    'المستخدمين المتصلين (آخر 5 دقائق)', 
    get_online_users_count(5);

SELECT '✅ تم إنشاء جميع المكونات بنجاح!' as final_status;
SELECT '🎯 الآن يمكن لتطبيق الكاميرا استخدام النظام الجديد' as next_step;
