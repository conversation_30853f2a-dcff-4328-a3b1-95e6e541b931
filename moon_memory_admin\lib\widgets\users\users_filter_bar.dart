import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/users_provider.dart';
import 'create_user_dialog.dart';

class UsersFilterBar extends ConsumerStatefulWidget {
  const UsersFilterBar({super.key});

  @override
  ConsumerState<UsersFilterBar> createState() => _UsersFilterBarState();
}

class _UsersFilterBarState extends ConsumerState<UsersFilterBar> {
  final _searchController = TextEditingController();
  String? _selectedDepartment;
  String? _selectedAccountType;
  bool? _selectedIsActive;
  bool? _selectedIsAdmin;

  @override
  void initState() {
    super.initState();
    final filter = ref.read(usersProvider).filter;
    _searchController.text = filter.searchQuery ?? '';
    _selectedDepartment = filter.department;
    _selectedAccountType = filter.accountType;
    _selectedIsActive = filter.isActive;
    _selectedIsAdmin = filter.isAdmin;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final usersState = ref.watch(usersProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: Card(
        elevation: 1,
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الفلترة
              Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: AppColors.primaryGold,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'فلترة وبحث',
                    style: const TextStyle(
                      color: AppColors.primaryGold,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const Spacer(),
                  if (usersState.filter.hasActiveFilters)
                    TextButton.icon(
                      onPressed: _clearFilters,
                      icon: const Icon(Icons.clear, size: 14),
                      label: const Text('مسح الفلاتر', style: TextStyle(fontSize: 12)),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        minimumSize: Size.zero,
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),
              
              // شريط البحث والفلاتر
              Row(
                children: [
                  // مربع البحث
                  Expanded(
                    flex: 2,
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'البحث بالاسم، البريد الإلكتروني، أو الرقم الوطني...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                onPressed: () {
                                  _searchController.clear();
                                  _applyFilters();
                                },
                                icon: const Icon(Icons.clear),
                              )
                            : null,
                      ),
                      onChanged: (value) {
                        // تطبيق البحث مع تأخير
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (_searchController.text == value) {
                            _applyFilters();
                          }
                        });
                      },
                    ),
                  ),



                  const SizedBox(width: 16),
                  
                  // فلتر الحالة
                  SizedBox(
                    width: 120,
                    child: DropdownButtonFormField<bool?>(
                      value: _selectedIsActive,
                      decoration: const InputDecoration(
                        labelText: 'الحالة',
                        prefixIcon: Icon(Icons.toggle_on, size: 16),
                        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        labelStyle: TextStyle(fontSize: 12),
                      ),
                      style: const TextStyle(fontSize: 12),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('الكل', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: true, child: Text('نشط', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: false, child: Text('غير نشط', style: TextStyle(fontSize: 12))),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedIsActive = value;
                        });
                        _applyFilters();
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // فلتر نوع المستخدم
                  SizedBox(
                    width: 120,
                    child: DropdownButtonFormField<bool?>(
                      value: _selectedIsAdmin,
                      decoration: const InputDecoration(
                        labelText: 'النوع',
                        prefixIcon: Icon(Icons.person, size: 16),
                        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        labelStyle: TextStyle(fontSize: 12),
                      ),
                      style: const TextStyle(fontSize: 12),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('الكل', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: true, child: Text('مدير', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: false, child: Text('مستخدم', style: TextStyle(fontSize: 12))),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedIsAdmin = value;
                        });
                        _applyFilters();
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // فلتر القسم
                  Expanded(
                    flex: 1,
                    child: DropdownButtonFormField<String?>(
                      value: _selectedDepartment,
                      decoration: const InputDecoration(
                        labelText: 'القسم',
                        prefixIcon: Icon(Icons.business, size: 16),
                        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        labelStyle: TextStyle(fontSize: 12),
                      ),
                      style: const TextStyle(fontSize: 12),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('الكل', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: 'تقنية المعلومات', child: Text('تقنية', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: 'الهندسة', child: Text('هندسة', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: 'الطب', child: Text('طب', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: 'الإدارة', child: Text('إدارة', style: TextStyle(fontSize: 12))),
                        DropdownMenuItem(value: 'أخرى', child: Text('أخرى', style: TextStyle(fontSize: 12))),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedDepartment = value;
                        });
                        _applyFilters();
                      },
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر إنشاء حساب جديد
                  ElevatedButton.icon(
                    onPressed: () => _showCreateUserDialog(context),
                    icon: const Icon(Icons.person_add, size: 18),
                    label: const Text('إنشاء حساب جديد', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryGold,
                      foregroundColor: AppColors.darkBackground,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      minimumSize: Size.zero,
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
              
              // مؤشر الفلاتر النشطة
              if (usersState.filter.hasActiveFilters) ...[
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  children: [
                    if (usersState.filter.searchQuery?.isNotEmpty == true)
                      _buildFilterChip('البحث: ${usersState.filter.searchQuery}'),
                    if (usersState.filter.isActive != null)
                      _buildFilterChip(
                        'الحالة: ${usersState.filter.isActive! ? "نشط" : "غير نشط"}',
                      ),
                    if (usersState.filter.isAdmin != null)
                      _buildFilterChip(
                        'النوع: ${usersState.filter.isAdmin! ? "مدير" : "مستخدم عادي"}',
                      ),
                    if (usersState.filter.department?.isNotEmpty == true)
                      _buildFilterChip('القسم: ${usersState.filter.department}'),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label) {
    return Chip(
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      backgroundColor: AppColors.primaryGold.withValues(alpha: 0.1),
      side: BorderSide(color: AppColors.primaryGold.withValues(alpha: 0.3)),
      labelStyle: TextStyle(color: AppColors.primaryGold),
    );
  }

  void _applyFilters() {
    final filter = UsersFilter(
      searchQuery: _searchController.text.trim().isEmpty 
          ? null 
          : _searchController.text.trim(),
      isActive: _selectedIsActive,
      isAdmin: _selectedIsAdmin,
      department: _selectedDepartment,
      accountType: _selectedAccountType,
    );
    
    ref.read(usersProvider.notifier).updateFilter(filter);
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedDepartment = null;
      _selectedAccountType = null;
      _selectedIsActive = null;
      _selectedIsAdmin = null;
    });

    ref.read(usersProvider.notifier).clearFilter();
  }

  void _showCreateUserDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CreateUserDialog(),
    );
  }

}
