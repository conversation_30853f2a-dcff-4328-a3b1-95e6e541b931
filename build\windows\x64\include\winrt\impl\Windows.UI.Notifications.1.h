// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Notifications_1_H
#define WINRT_Windows_UI_Notifications_1_H
#include "winrt/impl/Windows.UI.Notifications.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Notifications
{
    struct __declspec(empty_bases) IAdaptiveNotificationContent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdaptiveNotificationContent>
    {
        IAdaptiveNotificationContent(std::nullptr_t = nullptr) noexcept {}
        IAdaptiveNotificationContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdaptiveNotificationText :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdaptiveNotificationText>
    {
        IAdaptiveNotificationText(std::nullptr_t = nullptr) noexcept {}
        IAdaptiveNotificationText(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBadgeNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBadgeNotification>
    {
        IBadgeNotification(std::nullptr_t = nullptr) noexcept {}
        IBadgeNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBadgeNotificationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBadgeNotificationFactory>
    {
        IBadgeNotificationFactory(std::nullptr_t = nullptr) noexcept {}
        IBadgeNotificationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBadgeUpdateManagerForUser :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBadgeUpdateManagerForUser>
    {
        IBadgeUpdateManagerForUser(std::nullptr_t = nullptr) noexcept {}
        IBadgeUpdateManagerForUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBadgeUpdateManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBadgeUpdateManagerStatics>
    {
        IBadgeUpdateManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IBadgeUpdateManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBadgeUpdateManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBadgeUpdateManagerStatics2>
    {
        IBadgeUpdateManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IBadgeUpdateManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBadgeUpdater :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBadgeUpdater>
    {
        IBadgeUpdater(std::nullptr_t = nullptr) noexcept {}
        IBadgeUpdater(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownAdaptiveNotificationHintsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownAdaptiveNotificationHintsStatics>
    {
        IKnownAdaptiveNotificationHintsStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownAdaptiveNotificationHintsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownAdaptiveNotificationTextStylesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownAdaptiveNotificationTextStylesStatics>
    {
        IKnownAdaptiveNotificationTextStylesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownAdaptiveNotificationTextStylesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownNotificationBindingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownNotificationBindingsStatics>
    {
        IKnownNotificationBindingsStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownNotificationBindingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INotification>
    {
        INotification(std::nullptr_t = nullptr) noexcept {}
        INotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INotificationBinding :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INotificationBinding>
    {
        INotificationBinding(std::nullptr_t = nullptr) noexcept {}
        INotificationBinding(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INotificationData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INotificationData>
    {
        INotificationData(std::nullptr_t = nullptr) noexcept {}
        INotificationData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INotificationDataFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INotificationDataFactory>
    {
        INotificationDataFactory(std::nullptr_t = nullptr) noexcept {}
        INotificationDataFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INotificationVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INotificationVisual>
    {
        INotificationVisual(std::nullptr_t = nullptr) noexcept {}
        INotificationVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledTileNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledTileNotification>
    {
        IScheduledTileNotification(std::nullptr_t = nullptr) noexcept {}
        IScheduledTileNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledTileNotificationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledTileNotificationFactory>
    {
        IScheduledTileNotificationFactory(std::nullptr_t = nullptr) noexcept {}
        IScheduledTileNotificationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledToastNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledToastNotification>
    {
        IScheduledToastNotification(std::nullptr_t = nullptr) noexcept {}
        IScheduledToastNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledToastNotification2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledToastNotification2>
    {
        IScheduledToastNotification2(std::nullptr_t = nullptr) noexcept {}
        IScheduledToastNotification2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledToastNotification3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledToastNotification3>
    {
        IScheduledToastNotification3(std::nullptr_t = nullptr) noexcept {}
        IScheduledToastNotification3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledToastNotification4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledToastNotification4>
    {
        IScheduledToastNotification4(std::nullptr_t = nullptr) noexcept {}
        IScheduledToastNotification4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledToastNotificationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledToastNotificationFactory>
    {
        IScheduledToastNotificationFactory(std::nullptr_t = nullptr) noexcept {}
        IScheduledToastNotificationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScheduledToastNotificationShowingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScheduledToastNotificationShowingEventArgs>
    {
        IScheduledToastNotificationShowingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IScheduledToastNotificationShowingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShownTileNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShownTileNotification>
    {
        IShownTileNotification(std::nullptr_t = nullptr) noexcept {}
        IShownTileNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileFlyoutNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileFlyoutNotification>
    {
        ITileFlyoutNotification(std::nullptr_t = nullptr) noexcept {}
        ITileFlyoutNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileFlyoutNotificationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileFlyoutNotificationFactory>
    {
        ITileFlyoutNotificationFactory(std::nullptr_t = nullptr) noexcept {}
        ITileFlyoutNotificationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileFlyoutUpdateManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileFlyoutUpdateManagerStatics>
    {
        ITileFlyoutUpdateManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ITileFlyoutUpdateManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileFlyoutUpdater :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileFlyoutUpdater>
    {
        ITileFlyoutUpdater(std::nullptr_t = nullptr) noexcept {}
        ITileFlyoutUpdater(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileNotification>
    {
        ITileNotification(std::nullptr_t = nullptr) noexcept {}
        ITileNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileNotificationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileNotificationFactory>
    {
        ITileNotificationFactory(std::nullptr_t = nullptr) noexcept {}
        ITileNotificationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileUpdateManagerForUser :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileUpdateManagerForUser>
    {
        ITileUpdateManagerForUser(std::nullptr_t = nullptr) noexcept {}
        ITileUpdateManagerForUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileUpdateManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileUpdateManagerStatics>
    {
        ITileUpdateManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ITileUpdateManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileUpdateManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileUpdateManagerStatics2>
    {
        ITileUpdateManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        ITileUpdateManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileUpdater :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileUpdater>
    {
        ITileUpdater(std::nullptr_t = nullptr) noexcept {}
        ITileUpdater(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileUpdater2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileUpdater2>
    {
        ITileUpdater2(std::nullptr_t = nullptr) noexcept {}
        ITileUpdater2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastActivatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastActivatedEventArgs>
    {
        IToastActivatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IToastActivatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastActivatedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastActivatedEventArgs2>
    {
        IToastActivatedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IToastActivatedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastCollection>
    {
        IToastCollection(std::nullptr_t = nullptr) noexcept {}
        IToastCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastCollectionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastCollectionFactory>
    {
        IToastCollectionFactory(std::nullptr_t = nullptr) noexcept {}
        IToastCollectionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastCollectionManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastCollectionManager>
    {
        IToastCollectionManager(std::nullptr_t = nullptr) noexcept {}
        IToastCollectionManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastDismissedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastDismissedEventArgs>
    {
        IToastDismissedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IToastDismissedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastFailedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastFailedEventArgs>
    {
        IToastFailedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IToastFailedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotification>
    {
        IToastNotification(std::nullptr_t = nullptr) noexcept {}
        IToastNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotification2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotification2>
    {
        IToastNotification2(std::nullptr_t = nullptr) noexcept {}
        IToastNotification2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotification3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotification3>
    {
        IToastNotification3(std::nullptr_t = nullptr) noexcept {}
        IToastNotification3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotification4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotification4>
    {
        IToastNotification4(std::nullptr_t = nullptr) noexcept {}
        IToastNotification4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotification6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotification6>
    {
        IToastNotification6(std::nullptr_t = nullptr) noexcept {}
        IToastNotification6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationActionTriggerDetail :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationActionTriggerDetail>
    {
        IToastNotificationActionTriggerDetail(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationActionTriggerDetail(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationFactory>
    {
        IToastNotificationFactory(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationHistory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationHistory>
    {
        IToastNotificationHistory(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationHistory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationHistory2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationHistory2>
    {
        IToastNotificationHistory2(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationHistory2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationHistoryChangedTriggerDetail :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationHistoryChangedTriggerDetail>
    {
        IToastNotificationHistoryChangedTriggerDetail(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationHistoryChangedTriggerDetail(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationHistoryChangedTriggerDetail2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationHistoryChangedTriggerDetail2>
    {
        IToastNotificationHistoryChangedTriggerDetail2(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationHistoryChangedTriggerDetail2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationManagerForUser :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationManagerForUser>
    {
        IToastNotificationManagerForUser(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationManagerForUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationManagerForUser2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationManagerForUser2>
    {
        IToastNotificationManagerForUser2(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationManagerForUser2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationManagerStatics>
    {
        IToastNotificationManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationManagerStatics2>
    {
        IToastNotificationManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationManagerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationManagerStatics4>
    {
        IToastNotificationManagerStatics4(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationManagerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotificationManagerStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationManagerStatics5>
    {
        IToastNotificationManagerStatics5(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationManagerStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotifier :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotifier>
    {
        IToastNotifier(std::nullptr_t = nullptr) noexcept {}
        IToastNotifier(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotifier2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotifier2>
    {
        IToastNotifier2(std::nullptr_t = nullptr) noexcept {}
        IToastNotifier2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToastNotifier3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotifier3>
    {
        IToastNotifier3(std::nullptr_t = nullptr) noexcept {}
        IToastNotifier3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserNotification>
    {
        IUserNotification(std::nullptr_t = nullptr) noexcept {}
        IUserNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserNotificationChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserNotificationChangedEventArgs>
    {
        IUserNotificationChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUserNotificationChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
