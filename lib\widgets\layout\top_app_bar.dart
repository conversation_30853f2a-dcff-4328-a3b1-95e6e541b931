import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../screens/main_layout.dart';

class TopAppBar extends ConsumerWidget {
  const TopAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPage = ref.watch(currentPageProvider);

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر طي القائمة الجانبية
          Consumer(
            builder: (context, ref, child) {
              final isCollapsed = ref.watch(sidebarCollapsedProvider);
              return IconButton(
                onPressed: () {
                  ref.read(sidebarCollapsedProvider.notifier).state = !isCollapsed;
                },
                icon: Icon(
                  isCollapsed ? Icons.menu_open : Icons.menu,
                  color: AppColors.secondaryText,
                ),
                tooltip: isCollapsed ? 'توسيع القائمة' : 'طي القائمة',
              );
            },
          ),

          const SizedBox(width: 16),

          // عنوان الصفحة الحالية
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _getPageTitle(currentPage),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryText,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _getPageSubtitle(currentPage),
                    style: TextStyle(
                      fontSize: 11,
                      color: AppColors.secondaryText,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ),
          
          const Spacer(),
          
          // أزرار الإجراءات السريعة
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر الإشعارات
              _buildActionButton(
                icon: Icons.notifications_outlined,
                tooltip: 'الإشعارات',
                onPressed: () {
                  _showNotifications(context);
                },
                badgeCount: 3,
              ),

              const SizedBox(width: 8),

              // زر البحث السريع
              _buildActionButton(
                icon: Icons.search,
                tooltip: 'البحث السريع',
                onPressed: () {
                  _showQuickSearch(context);
                },
              ),

              const SizedBox(width: 8),

              // زر التحديث
              _buildActionButton(
                icon: Icons.refresh,
                tooltip: 'تحديث البيانات',
                onPressed: () {
                  _refreshCurrentPage(ref, currentPage);
                },
              ),

              const SizedBox(width: 12),

              // زر الملف الشخصي
              _buildProfileButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    int? badgeCount,
  }) {
    return Tooltip(
      message: tooltip,
      child: Stack(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.surfaceBackground,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: AppColors.borderColor,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: onPressed,
              icon: Icon(
                icon,
                size: 20,
                color: AppColors.secondaryText,
              ),
              padding: EdgeInsets.zero,
            ),
          ),
          if (badgeCount != null && badgeCount > 0)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.error,
                  borderRadius: BorderRadius.circular(10),
                ),
                constraints: const BoxConstraints(
                  minWidth: 18,
                  minHeight: 18,
                ),
                child: Text(
                  badgeCount > 99 ? '99+' : badgeCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileButton() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 180),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.primaryGold.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryGold.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            radius: 14,
            backgroundColor: AppColors.primaryGold,
            child: const Text(
              'م',
              style: TextStyle(
                color: AppColors.darkBackground,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(width: 6),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'مدير النظام',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryText,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'متصل',
                  style: TextStyle(
                    fontSize: 9,
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 2),
          Icon(
            Icons.keyboard_arrow_down,
            size: 14,
            color: AppColors.secondaryText,
          ),
        ],
      ),
    );
  }

  String _getPageTitle(int pageIndex) {
    switch (pageIndex) {
      case 0:
        return 'لوحة المراقبة';
      case 1:
        return 'إدارة المستخدمين';
      case 2:
        return 'إدارة المواقع';
      case 3:
        return 'معرض الوسائط';
      case 4:
        return 'إدارة الأجهزة';
      case 5:
        return 'التقارير';
      case 6:
        return 'الإعدادات';
      case 7:
        return 'فحص قاعدة البيانات';
      default:
        return 'لوحة المراقبة';
    }
  }

  String _getPageSubtitle(int pageIndex) {
    switch (pageIndex) {
      case 0:
        return 'نظرة عامة على النظام والإحصائيات';
      case 1:
        return 'إدارة حسابات المستخدمين والصلاحيات';
      case 2:
        return 'إدارة المواقع والأماكن';
      case 3:
        return 'عرض وإدارة الصور والفيديوهات';
      case 4:
        return 'مراقبة الأجهزة المتصلة';
      case 5:
        return 'تقارير مفصلة وإحصائيات';
      case 6:
        return 'إعدادات النظام والتطبيق';
      case 7:
        return 'أدوات فحص واستكشاف قاعدة البيانات';
      default:
        return 'نظرة عامة على النظام والإحصائيات';
    }
  }

  void _showNotifications(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإشعارات'),
        content: const Text('لا توجد إشعارات جديدة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showQuickSearch(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث السريع'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'ابحث في النظام...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  void _refreshCurrentPage(WidgetRef ref, int currentPage) {
    // سنضيف منطق التحديث حسب الصفحة الحالية
    ScaffoldMessenger.of(ref.context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث البيانات'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
