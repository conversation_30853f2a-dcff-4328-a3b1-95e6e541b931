// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_System_RemoteDesktop_1_H
#define WINRT_Windows_System_RemoteDesktop_1_H
#include "winrt/impl/Windows.System.RemoteDesktop.0.h"
WINRT_EXPORT namespace winrt::Windows::System::RemoteDesktop
{
    struct __declspec(empty_bases) IInteractiveSessionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractiveSessionStatics>
    {
        IInteractiveSessionStatics(std::nullptr_t = nullptr) noexcept {}
        IInteractiveSessionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
