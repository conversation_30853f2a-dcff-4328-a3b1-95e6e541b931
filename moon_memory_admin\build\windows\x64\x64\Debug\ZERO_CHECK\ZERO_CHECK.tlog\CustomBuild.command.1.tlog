^C:\USERS\<USER>\CASCADEPROJECTS\MOON_MEMORY_V2\MOON_MEMORY_ADMIN\MOON_MEMORY_ADMIN\BUILD\WINDOWS\X64\CMAKEFILES\1D9DE88832EE210BF7AA3E54EF25391E\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/windows -BC:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/CascadeProjects/moon_memory_v2/moon_memory_admin/moon_memory_admin/build/windows/x64/moon_memory_admin.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
