# 🎯 دليل إنشاء المستخدمين الجديد - تسجيل دخول بالرقم الوطني

## 🔧 التحديثات الجديدة

تم تطوير نظام إنشاء المستخدمين ليصبح **متكاملاً مع تسجيل الدخول بالرقم الوطني فقط**. النظام ينشئ البريد الإلكتروني تلقائياً بصيغة `رقم_الهوية@moon-memory.com` دون الحاجة لإدخاله يدوياً.

---

## 🚀 الميزات الجديدة

### ✅ **إنشاء مستخدمين متكامل:**
- إنشاء في `auth.users` (نظام المصادقة)
- إنشاء في `public.users` (بيانات المستخدم)
- **إنشاء البريد الإلكتروني تلقائياً**: `رقم_الهوية@moon-memory.com`
- تزامن كامل بين الجدولين
- إمكانية تسجيل الدخول فوراً **بالرقم الوطني وكلمة المرور**

### ✅ **واجهة محسنة:**
- نافذة إنشاء مبسطة **بدون حقل البريد الإلكتروني**
- عرض بيانات تسجيل الدخول تعرض **الرقم الوطني وكلمة المرور**
- إمكانية نسخ البيانات
- رسائل تأكيد واضحة

### ✅ **أمان محسن:**
- استخدام Service Role Key للعمليات الإدارية
- تشفير كلمات المرور تلقائياً
- صلاحيات محددة لكل عملية

---

## 🔑 متطلبات التشغيل

### 1. **الحصول على Service Role Key:**

1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. اختر مشروعك `moon-memory-v2`
3. اذهب إلى **Settings** → **API**
4. انسخ **service_role** key
5. استبدله في الملف: `lib/core/constants/app_constants.dart`

```dart
// استبدل هذا المفتاح بالمفتاح الحقيقي
static const String supabaseServiceRoleKey = 'YOUR_REAL_SERVICE_ROLE_KEY_HERE';
```

### 2. **تفعيل صلاحيات قاعدة البيانات:**

نفذ هذا السكريبت في **SQL Editor** في Supabase:

```sql
-- إزالة قيود RLS للعمليات الإدارية
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE devices DISABLE ROW LEVEL SECURITY;
ALTER TABLE photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE locations DISABLE ROW LEVEL SECURITY;

-- منح صلاحيات كاملة
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- تأكيد الإعدادات
SELECT 'تم تفعيل الصلاحيات الكاملة بنجاح! 🎉' as status;
```

---

## 📋 كيفية إنشاء مستخدم جديد

### **الخطوات:**

1. **افتح تطبيق الإدارة**
2. **اذهب إلى قسم "المستخدمين"**
3. **اضغط على "إنشاء حساب جديد"**
4. **املأ البيانات المطلوبة:**
   - الرقم الوطني (10 أرقام) - **سيستخدم لتسجيل الدخول**
   - الاسم الكامل
   - رقم الهاتف (اختياري)
   - القسم
   - المنصب
   - كلمة المرور (8 أحرف على الأقل)
   - نوع الحساب (مدير/مستخدم عادي)
   - حالة الحساب (نشط/غير نشط)

5. **اضغط "إنشاء الحساب"**
6. **ستظهر نافذة بيانات تسجيل الدخول**
7. **انسخ البيانات وأرسلها للمستخدم**

### **بيانات تسجيل الدخول:**
- **الرقم الوطني:** الذي أدخلته في النموذج (للدخول للتطبيق)
- **كلمة المرور:** التي أدخلتها في النموذج
- **البريد الإلكتروني:** يتم إنشاؤه تلقائياً `رقم_الهوية@moon-memory.com` (للنظام فقط)

---

## 🎯 أمثلة عملية

### **مثال 1: إنشاء مستخدم عادي**
```
الرقم الوطني: 1234567890
الاسم الكامل: أحمد محمد علي
القسم: قسم المحاسبة
المنصب: محاسب
كلمة المرور: Ahmed123456
نوع الحساب: مستخدم عادي ✓
حالة الحساب: نشط ✓
```

**النتيجة:**
- الرقم الوطني: `1234567890` (للدخول للتطبيق)
- كلمة المرور: `Ahmed123456`
- البريد الإلكتروني: `<EMAIL>` (تلقائي)
- يمكن للمستخدم تسجيل الدخول فوراً في التطبيق الرئيسي **بالرقم الوطني وكلمة المرور**

### **مثال 2: إنشاء مدير**
```
الرقم الوطني: 9876543210
الاسم الكامل: سارة أحمد محمد
القسم: الإدارة العامة
المنصب: مدير عام
كلمة المرور: Sara987654
نوع الحساب: مدير ✓
حالة الحساب: نشط ✓
```

**النتيجة:**
- الرقم الوطني: `9876543210` (للدخول للتطبيق)
- كلمة المرور: `Sara987654`
- البريد الإلكتروني: `<EMAIL>` (تلقائي)
- صلاحيات إدارية كاملة

---

## 🔍 التحقق من نجاح العملية

### **في تطبيق الإدارة:**
1. تحديث قائمة المستخدمين تلقائياً
2. ظهور المستخدم الجديد في الجدول
3. عرض رسالة نجاح
4. إمكانية رؤية المستخدم في قسم "المديرين" (إذا كان مديراً)

### **في التطبيق الرئيسي:**
1. المستخدم يمكنه تسجيل الدخول فوراً
2. استخدام البريد الإلكتروني وكلمة المرور
3. ربط الجهاز تلقائياً عند أول دخول
4. الوصول لجميع الوظائف حسب الصلاحيات

---

## 🛠️ استكشاف الأخطاء

### **خطأ: "فشل في إنشاء المستخدم في نظام المصادقة"**
**الحل:**
- تأكد من Service Role Key الصحيح
- تحقق من اتصال الإنترنت
- تأكد من صحة بيانات البريد الإلكتروني

### **خطأ: "البريد الإلكتروني مستخدم بالفعل"**
**الحل:**
- استخدم بريد إلكتروني مختلف
- أو احذف المستخدم القديم أولاً

### **خطأ: "فشل في تغيير حالة المستخدم"**
**الحل:**
- تطبيق سكريبت إزالة قيود RLS
- تحقق من صلاحيات قاعدة البيانات

---

## 📞 الدعم التقني

### **للمشاكل التقنية:**
1. تحقق من Service Role Key
2. تطبيق سكريبت الصلاحيات
3. إعادة تشغيل التطبيق
4. فحص رسائل الخطأ في وحدة التحكم

### **للمساعدة الإضافية:**
- راجع ملف `ADMIN_GUIDE.md` للعمليات اليدوية
- استخدم SQL Editor في Supabase للفحص المباشر
- تحقق من جدولي `auth.users` و `public.users`

---

## 🎉 النتيجة النهائية

**بعد تطبيق هذا النظام:**
- ✅ إنشاء مستخدمين متكامل 100%
- ✅ تسجيل دخول فوري للمستخدمين الجدد
- ✅ واجهة إدارية احترافية
- ✅ أمان عالي ومعالجة أخطاء شاملة
- ✅ تجربة مستخدم متميزة

**النظام جاهز للاستخدام الفوري!** 🚀
