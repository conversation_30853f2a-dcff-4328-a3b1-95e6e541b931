import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_colors.dart';
import '../providers/users_provider.dart';
import '../models/user_model.dart';
import '../widgets/users/users_filter_bar.dart';
import '../widgets/users/users_data_table.dart';
import '../widgets/users/users_stats_bar.dart';


class UsersScreen extends ConsumerWidget {
  const UsersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersState = ref.watch(usersProvider);

    return Column(
      children: [
        // أزرار الإجراءات
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          child: Row(
            children: [
              const Spacer(),
              OutlinedButton.icon(
                onPressed: () {
                  ref.read(usersProvider.notifier).refresh();
                },
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('تحديث القائمة', style: TextStyle(fontSize: 12)),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  minimumSize: Size.zero,
                ),
              ),
            ],
          ),
        ),
            
            // شريط الإحصائيات
            const UsersStatsBar(),



            // شريط الفلترة
            const UsersFilterBar(),
            
            // جدول المستخدمين
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                child: Card(
                  elevation: 2,
                  child: Column(
                    children: [
                      // رأس الجدول
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppColors.surfaceBackground,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              'قائمة المستخدمين',
                              style: const TextStyle(
                                color: AppColors.primaryGold,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            const Spacer(),
                            if (usersState.isLoading)
                              const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppColors.primaryGold,
                                ),
                              ),
                            const SizedBox(width: 16),
                            Text(
                              '${usersState.totalCount} مستخدم',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.secondaryText,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // محتوى الجدول
                      Expanded(
                        child: usersState.error != null
                            ? _buildErrorState(context, usersState.error!, ref)
                            : const UsersDataTable(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
  }

  Widget _buildErrorState(BuildContext context, String error, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل المستخدمين',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(usersProvider.notifier).refresh();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }



  // عرض المستخدمين المتصلين الآن
  void _showOnlineUsersDialog(BuildContext context, List<UserModel> users) {
    final onlineUsers = users.where((u) => u.isActive).toList();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.people, color: AppColors.success, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'المستخدمين المتصلين الآن',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.success,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${onlineUsers.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.white.withValues(alpha: 0.1),
                      ),
                    ),
                  ],
                ),
              ),

              // قائمة المستخدمين
              Expanded(
                child: onlineUsers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.people_outline,
                              size: 64,
                              color: AppColors.secondaryText,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا يوجد مستخدمين متصلين حالياً',
                              style: TextStyle(
                                color: AppColors.secondaryText,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: onlineUsers.length,
                        itemBuilder: (context, index) {
                          final user = onlineUsers[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              leading: Stack(
                                children: [
                                  CircleAvatar(
                                    backgroundColor: user.isAdmin
                                        ? AppColors.primaryGold
                                        : AppColors.info,
                                    child: Text(
                                      user.fullName?.substring(0, 1) ?? 'م',
                                      style: TextStyle(
                                        color: user.isAdmin
                                            ? AppColors.darkBackground
                                            : Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: AppColors.success,
                                        shape: BoxShape.circle,
                                        border: Border.all(color: Colors.white, width: 2),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              title: Text(
                                user.fullName ?? 'غير محدد',
                                style: const TextStyle(fontWeight: FontWeight.w600),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(user.email ?? 'لا يوجد بريد إلكتروني'),
                                  const SizedBox(height: 2),
                                  Row(
                                    children: [
                                      Icon(
                                        user.isAdmin ? Icons.admin_panel_settings : Icons.person,
                                        size: 14,
                                        color: user.isAdmin ? AppColors.primaryGold : AppColors.info,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        user.isAdmin ? 'مدير' : 'مستخدم',
                                        style: TextStyle(
                                          color: user.isAdmin ? AppColors.primaryGold : AppColors.info,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Icon(Icons.access_time, size: 14, color: AppColors.secondaryText),
                                      const SizedBox(width: 4),
                                      Text(
                                        user.lastLogin != null
                                            ? _formatLastLogin(user.lastLogin!)
                                            : 'غير محدد',
                                        style: TextStyle(
                                          color: AppColors.secondaryText,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              trailing: Icon(
                                Icons.circle,
                                color: AppColors.success,
                                size: 12,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatLastLogin(DateTime lastLogin) {
    final now = DateTime.now();
    final difference = now.difference(lastLogin);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }


}
